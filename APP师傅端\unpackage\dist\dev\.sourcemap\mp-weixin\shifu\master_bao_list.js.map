{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_bao_list.vue?1a32", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_bao_list.vue?6a19", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_bao_list.vue?0df9", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_bao_list.vue?2c7d", "uni-app:///shifu/master_bao_list.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_bao_list.vue?a62c", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_bao_list.vue?5248"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "goodsId", "page", "pageSize", "list", "show", "input", "shifuId", "id", "userInfo", "showCancel", "scrollToId", "loadingStatus", "loadingText", "hasMore", "total", "tabId", "fromQuote", "isRefreshing", "isLoadingMore", "onPullDownRefresh", "console", "onReachBottom", "onShow", "methods", "refreshData", "uni", "loadMoreData", "getList", "coachId", "pageNum", "setTimeout", "resolve", "icon", "title", "reject", "cancelBao", "cancelModal", "confirm", "again<PERSON><PERSON>", "close", "confirmBao", "orderId", "price", "onUnload", "delta", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4RAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpDA;AAAA;AAAA;AAAA;AAA81B,CAAgB,82BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCsFl3B;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACA;EACAC;IACAC;IACA;EACA;EACA;EACAC;IACAD;IACA;MACA;MACA;IACA;EACA;EACA;EACAE;IACAF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAG;IACA;IACAC;MAAA;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;MAEA;QACA;QACA;QACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEA;MACA;MACA;MAEA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;MACA;QACA;UACAC;UAEAC;UACA3B;QACA;UACAkB;UACA;YACA;YACA;YACA;YACA;YACA;YACAU;cACA;YACA;YACAC;YACA;UACA;UACA;UACA;;UAEA;UACA;YAAA;UAAA;UAEA;YACA;YACA;UACA;YACA;YACA;UACA;;UAEA;UACA;;UAEA;UACA;YACA;YACA;YACAD;cACA;YACA;UACA;UAEAC;QACA;UACAX;;UAEA;UACA;YACA;UACA;;UAEA;UACA;UACAK;YACAO;YACAC;UACA;UAEAC;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;MAEA;QACA9B;MACA;QACAa;QACA;UACAK;YACAO;YACAC;UACA;UACA;QACA;UACAR;YACAO;YACAC;UACA;QACA;;QAEA;QACA;MACA;QACA;QACAR;UACAO;UACAC;QACA;MACA;IACA;IAEA;IACAK;MAAA;MACAlB;MACA;MACA;MACA;MACA;MACA;MACAU;QACA;MACA;IACA;IAEA;IACAS;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACAf;UACAO;UACAC;QACA;QACA;MACA;;MAEA;MACA;MACA;QACAR;UACAO;UACAC;QACA;QACA;MACA;MAEA;MACA;MAEA;QACAQ;QACAC;QACA1C;MACA;QACAoB;QACA;UACAK;YACAO;YACAC;UACA;QAEA;UACAb;UACAK;YACAO;YACAC;UACA;UACA;UACA;UACA;QACA;MACA;QACA;QACAR;UACAO;UACAC;QACA;QACA;MACA;IACA;EACA;EACA;EACA;EACA;EACAU;IACA;IACA;MACA;MACAlB;MACA;MACAA;QACAmB;MACA;IACA;EACA;EACA;EACAC;IAAA;IACAzB;IACA;IACA;IACA;IACA;MACA;MACA;QACA;QACA;QACAA;MACA;MAEA;MACAA;;MAEA;MACA;MACA;MACA;QACA;MACA;IACA;MACAA;MACAK;QACAO;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjZA;AAAA;AAAA;AAAA;AAAqmD,CAAgB,yjDAAG,EAAC,C;;;;;;;;;;;ACAznD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/master_bao_list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/master_bao_list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./master_bao_list.vue?vue&type=template&id=0cdcf780&scoped=true&\"\nvar renderjs\nimport script from \"./master_bao_list.vue?vue&type=script&lang=js&\"\nexport * from \"./master_bao_list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./master_bao_list.vue?vue&type=style&index=0&id=0cdcf780&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0cdcf780\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/master_bao_list.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_bao_list.vue?vue&type=template&id=0cdcf780&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    \"u-Input\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--input/u--input\" */ \"uview-ui/components/u--input/u--input.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-modal/u-modal\" */ \"uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length === 0 && !_vm.loadingStatus\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_bao_list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_bao_list.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<view class=\"main\">\r\n\t\t\t<!-- Order list with dynamic rendering -->\r\n\t\t\t<view class=\"main_item_already\" v-for=\"(item, index) in list\" :key=\"index\">\r\n\t\t\t\t<!-- Display order status based on payType -->\r\n\t\t\t\t<view style=\"display: flex; justify-content: space-between;\" class=\"\">\r\n\t\t\t\t\t<view class=\"title\">{{ item.payType == -2 ? '等待客户选择' : '' }}</view>\r\n\t\t\t\t\t<view class=\"title\">{{ item.payType == -1 ? '客户已取消订单' : '' }}</view>\r\n\t\t\t\t\t<view class=\"title\">{{ item.payType == 1 ? '客户已选择报价' : '' }}</view>\r\n\t\t\t\t\t<view style=\"padding-right: 40rpx; \" class=\"\">\r\n\t\t\t\t\t\t剩余报价次数:   {{item.remainingNum}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t<!-- Indicate that a bid has been placed -->\r\n\t\t\t\t<view class=\"ok\">您已报价</view>\r\n\t\t\t\t<!-- Display order number -->\r\n\t\t\t\t<view class=\"no\">单号：{{ item.orderCode }}</view>\r\n\t\t\t\t<!-- Order details: image and name -->\r\n\t\t\t\t<view class=\"mid\">\r\n\t\t\t\t\t<view class=\"lef\">\r\n\t\t\t\t\t\t<image :src=\"item.goodsCover\" mode=\"\"></image>\r\n\t\t\t\t\t\t<text>{{ item.goodsName }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- Order timestamp -->\r\n\t\t\t\t<view class=\"bot\">\r\n\t\t\t\t\t<text>{{item.orderTime}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- Service provider info and bid price -->\r\n\t\t\t\t<view class=\"shifu\">\r\n\t\t\t\t\t<scroll-view scroll-x=\"true\">\r\n\t\t\t\t\t\t<view class=\"shifu_item\">\r\n\t\t\t\t\t\t\t<view class=\"top\">\r\n\t\t\t\t\t\t\t\t<image :src=\"userInfo.avatarUrl?userInfo.avatarUrl:'/static/mine/default_user.png'\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"name\">{{ userInfo.nickName }}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text>￥{{ item.price }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- Buttons for canceling or re-bidding, shown only if payType is -2 (awaiting selection) -->\r\n\t\t\t\t<view class=\"btnbox\" v-if=\"item.payType == -2\">\r\n\t\t\t\t\t<view class=\"btn can\" @click=\"cancelBao(item)\">取消报价</view>\r\n\t\t\t\t\t<view class=\"btn re\" @click=\"againBao(item)\">重新报价</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- Loading status indicator -->\r\n\t\t\t<view class=\"loading-status\" v-if=\"loadingStatus\">\r\n\t\t\t\t<text>{{ loadingText }}</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- Empty data message -->\r\n\t\t\t<view class=\"empty-data\" v-if=\"list.length === 0 && !loadingStatus\">\r\n\t\t\t\t<text>暂无数据</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- Popup for re-bidding -->\r\n\t\t<u-popup :show=\"show\" :round=\"10\" closeable @close=\"close\" :adjust-position=\"true\" mode=\"bottom\">\r\n\t\t\t<scroll-view scroll-y=\"true\" class=\"popup-scroll\" :scroll-into-view=\"scrollToId\">\r\n\t\t\t\t<view class=\"box\" id=\"input-box\">\r\n\t\t\t\t\t<view class=\"title\">重新报价</view>\r\n\t\t\t\t\t<view class=\"title2\">报价金额</view>\r\n\t\t\t\t\t<view class=\"money\">\r\n\t\t\t\t\t\t<u--input id=\"price-input\" placeholder=\"请输入报价金额\" prefixIcon=\"rmb\"\r\n\t\t\t\t\t\t\tprefixIconStyle=\"font-size: 22px;color: #909399\" type=\"digit\" v-model=\"input\"\r\n\t\t\t\t\t\t\tfocus></u--input>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"btn\" @click=\"confirmBao\">确认报价</view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</u-popup>\r\n\r\n\t\t<!-- Modal for confirming bid cancellation -->\r\n\t\t<u-modal :show=\"showCancel\" title=\"取消报价\" content=\"确定要取消对本单的报价吗？\" showCancelButton @cancel=\"cancelModal\"\r\n\t\t\t@confirm=\"confirm\"></u-modal>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tgoodsId:0,\r\n\t\t\t\tpage: 1, // Current page number\r\n\t\t\t\tpageSize: 10, // Number of items per page\r\n\t\t\t\tlist: [], // List of orders\r\n\t\t\t\tshow: false, // Controls re-bid popup visibility\r\n\t\t\t\tinput: '', // Re-bid input value\r\n\t\t\t\tshifuId: '', // Service provider ID\r\n\t\t\t\tid: '', // Current order ID for actions\r\n\t\t\t\tuserInfo: '', // User information\r\n\t\t\t\tshowCancel: false, // Controls cancel modal visibility\r\n\t\t\t\tscrollToId: '', // ID for scrolling in popup\r\n\t\t\t\tloadingStatus: false, // Loading state flag\r\n\t\t\t\tloadingText: '', // Loading message\r\n\t\t\t\thasMore: true, // Whether more data is available\r\n\t\t\t\ttotal: 0, // Total number of orders\r\n\t\t\t\ttabId:\"0\",\r\n\t\t\t\tfromQuote: false, // 标识是否从报价页面跳转过来\r\n\t\t\t\tisRefreshing: false, // Prevents multiple refreshes\r\n\t\t\t\tisLoadingMore: false // Prevents multiple load more requests\r\n\t\t\t};\r\n\t\t},\r\n\t\t// Handle pull-down refresh\r\n\t\tonPullDownRefresh() {\r\n\t\t\tconsole.log('refresh');\r\n\t\t\tthis.refreshData();\r\n\t\t},\r\n\t\t// Handle reaching the bottom of the page\r\n\t\tonReachBottom() {\r\n\t\t\tconsole.log('reach bottom');\r\n\t\t\tif (this.list.length < this.total && !this.isLoadingMore) {\r\n\t\t\t\tthis.page += 1;\r\n\t\t\t\tthis.loadMoreData();\r\n\t\t\t}\r\n\t\t},\r\n\t\t// Handle page show\r\n\t\tonShow() {\r\n\t\t\tconsole.log('page show');\r\n\t\t},\r\n\t\t// onUnload() {\r\n\t\t// \tuni.setStorageSync('refreshReceiving', true);\r\n\t\t// \tthis.$store.commit('setRefreshReceiving', true);\r\n\t\t// \t\tuni.navigateBack({\r\n\t\t// \t\t\tdelta:1\r\n\t\t// \t\t})\r\n\t\t// \t},\r\n\t\tmethods: {\r\n\t\t\t// Refresh the order list\r\n\t\t\trefreshData() {\r\n\t\t\t\tif (this.isRefreshing) return;\r\n\r\n\t\t\t\tthis.isRefreshing = true;\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.list = [];\r\n\t\t\t\tthis.hasMore = true;\r\n\t\t\t\tthis.loadingStatus = true;\r\n\t\t\t\tthis.loadingText = '正在刷新...';\r\n\r\n\t\t\t\tthis.getList(false).finally(() => {\r\n\t\t\t\t\tthis.isRefreshing = false;\r\n\t\t\t\t\tthis.loadingStatus = false;\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// Load more orders\r\n\t\t\tloadMoreData() {\r\n\t\t\t\tif (this.isLoadingMore || !this.hasMore) return;\r\n\r\n\t\t\t\tthis.isLoadingMore = true;\r\n\t\t\t\tthis.loadingStatus = true;\r\n\t\t\t\tthis.loadingText = '正在加载更多...';\r\n\r\n\t\t\t\tthis.getList(true).finally(() => {\r\n\t\t\t\t\tthis.isLoadingMore = false;\r\n\t\t\t\t\tthis.loadingStatus = false;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// Fetch order list from API\r\n\t\t\tgetList(append = false) {\r\n\t\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t\tthis.$api.shifu.masterBaolist({\r\n\t\t\t\t\t\tcoachId: this.userInfo.shifuId,\r\n\t\t\t\t\r\n\t\t\t\t\t\tpageNum: this.page,\r\n\t\t\t\t\t\tpageSize: this.pageSize\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\tif (!res.data) {\r\n\t\t\t\t\t\t\tthis.total = 0;\r\n\t\t\t\t\t\t\tthis.list = append ? [...this.list] : [];\r\n\t\t\t\t\t\t\tthis.hasMore = false;\r\n\t\t\t\t\t\t\tthis.loadingText = '没有数据';\r\n\t\t\t\t\t\t\tthis.loadingStatus = true;\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tthis.loadingStatus = false;\r\n\t\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t\t\tresolve(res);\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// Update total count\r\n\t\t\t\t\t\tthis.total = res.data.totalCount || 0;\r\n\r\n\t\t\t\t\t\t// Sort new data by order time (descending)\r\n\t\t\t\t\t\tconst newList = (res.data.list || []).sort((a, b) => b.orderTime - a.orderTime);\r\n\r\n\t\t\t\t\t\tif (append) {\r\n\t\t\t\t\t\t\t// Append new data to existing list\r\n\t\t\t\t\t\t\tthis.list = [...this.list, ...newList];\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// Replace list with new data\r\n\t\t\t\t\t\t\tthis.list = newList;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// Check if more data is available\r\n\t\t\t\t\t\tthis.hasMore = this.list.length < this.total && newList.length === this.pageSize;\r\n\r\n\t\t\t\t\t\t// Show message if no more data\r\n\t\t\t\t\t\tif (!this.hasMore && this.list.length > 0) {\r\n\t\t\t\t\t\t\tthis.loadingText = '没有更多数据了';\r\n\t\t\t\t\t\t\tthis.loadingStatus = true;\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tthis.loadingStatus = false;\r\n\t\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tresolve(res);\r\n\t\t\t\t\t}).catch(e => {\r\n\t\t\t\t\t\tconsole.error('获取列表失败:', e);\r\n\r\n\t\t\t\t\t\t// Roll back page number on load more failure\r\n\t\t\t\t\t\tif (append && this.page > 1) {\r\n\t\t\t\t\t\t\tthis.page -= 1;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// Show error toast\r\n\t\t\t\t\t\tconst errorMsg = typeof e === 'string' ? e : e.message || '请成为师傅';\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\ttitle: errorMsg\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\treject(e);\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// Open cancel bid modal\r\n\t\t\tcancelBao(item) {\r\n\t\t\t\tthis.showCancel = true;\r\n\t\t\t\tthis.id = item.orderId;\r\n\t\t\t},\r\n\r\n\t\t\t// Close cancel modal\r\n\t\t\tcancelModal() {\r\n\t\t\t\tthis.showCancel = false;\r\n\t\t\t},\r\n\r\n\t\t\t// Confirm bid cancellation\r\n\t\t\tconfirm() {\r\n\t\t\t\tthis.showCancel = false;\r\n\t\t\t\tthis.loadingStatus = true;\r\n\t\t\t\tthis.loadingText = '正在取消...';\r\n\r\n\t\t\t\tthis.$api.shifu.updateQuxiaoBao({\r\n\t\t\t\t\tid: this.id,\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\tif(res.code===\"-1\"){\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\ttitle: res.msg\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\ttitle: '取消成功'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t\t// Refresh data\r\n\t\t\t\t\tthis.refreshData();\r\n\t\t\t\t}).catch(e => {\r\n\t\t\t\t\tthis.loadingStatus = false;\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: typeof e === 'string' ? e : e.message || '取消失败，请重试'\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// Open re-bid popup\r\n\t\t\tagainBao(item) {\r\n\t\t\t\tconsole.log(item)\r\n\t\t\t\tthis.goodsId=item.id\r\n\t\t\t\tthis.show = true;\r\n\t\t\t\tthis.id = item.orderId;\r\n\t\t\t\tthis.scrollToId = 'input-box';\r\n\t\t\t\t// Ensure scrolling after popup renders\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.scrollToId = 'input-box';\r\n\t\t\t\t}, 100);\r\n\t\t\t},\r\n\r\n\t\t\t// Close re-bid popup\r\n\t\t\tclose() {\r\n\t\t\t\tthis.show = false;\r\n\t\t\t\tthis.input = '';\r\n\t\t\t\tthis.scrollToId = '';\r\n\t\t\t},\r\n\r\n\t\t\t// Submit new bid\r\n\t\t\tconfirmBao() {\r\n\t\t\t\tif (this.input === '' || this.input == 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: '请输入报价(不能为0哦)'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Validate input as a positive number\r\n\t\t\t\tconst price = parseFloat(this.input);\r\n\t\t\t\tif (isNaN(price) || price <= 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: '请输入有效的报价金额'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.loadingStatus = true;\r\n\t\t\t\tthis.loadingText = '正在提交报价...';\r\n\r\n\t\t\t\tthis.$api.shifu.updateBao({\r\n\t\t\t\t\torderId: this.id,\r\n\t\t\t\t\tprice: this.input,\r\n\t\t\t\t\tgoodsId:this.goodsId,\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\tif (res.code === \"-1\") {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: 'error',\r\n\t\t\t\t\t\t\ttitle: res.msg\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\ttitle: '报价成功'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.close();\r\n\t\t\t\t\t\t// Refresh data\r\n\t\t\t\t\t\tthis.refreshData();\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(e => {\r\n\t\t\t\t\tthis.loadingStatus = false;\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: typeof e === 'string' ? e : e.message || '报价失败，请重试'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.close();\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t// onUnload() {\r\n\t\t//   uni.setStorageSync('refreshReceiving', true);\r\n\t\t// },\r\n\t\tonUnload() {\r\n\t\t\t// 如果是从报价页面跳转过来的，或者是报价订单tab，则需要刷新首页\r\n\t\t\tif(this.tabId===\"1\" || this.fromQuote){\r\n\t\t\t\t// 触发首页刷新事件\r\n\t\t\t\tuni.$emit('refreshReceivingList');\r\n\t\t\t\t// 返回上一页\r\n\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\tdelta: 1\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t// Initialize data on page load\r\n\t\tonLoad(option) {\r\n\t\t\tconsole.log(option)\r\n\t\t\tthis.tabId=option.id\r\n\t\t\tthis.goodsId=option.goodsId\r\n\t\t\tthis.fromQuote = option.fromQuote === 'true' // 接收从报价页面跳转的标识\r\n\t\t\ttry {\r\n\t\t\t\tlet shiInfo = uni.getStorageSync('shiInfo') || '';\r\n\t\t\t\tif (shiInfo) {\r\n\t\t\t\t\tconst parsedInfo = JSON.parse(shiInfo);\r\n\t\t\t\t\tthis.shifuId = parsedInfo.id;\r\n\t\t\t\t\tconsole.log('师傅ID:', this.shifuId);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.userInfo = uni.getStorageSync('userInfo') || {};\r\n\t\t\t\tconsole.log('用户信息:', this.userInfo);\r\n\t\t\t\t\t\r\n\t\t\t\t// Initial data load\r\n\t\t\t\tthis.loadingStatus = true;\r\n\t\t\t\tthis.loadingText = '正在加载...';\r\n\t\t\t\tthis.getList().finally(() => {\r\n\t\t\t\t\tthis.loadingStatus = false;\r\n\t\t\t\t});\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('初始化失败:', error);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '初始化失败'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.page {\r\n\t\tbackground-color: #F8F8F8;\r\n\t\tmin-height: 100vh;\r\n\r\n\t\t.popup-scroll {\r\n\t\t\tmax-height: 60vh;\r\n\t\t}\r\n\r\n\t\t.box {\r\n\t\t\tpadding: 40rpx 30rpx;\r\n\r\n\t\t\t.title {\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #171717;\r\n\t\t\t}\r\n\r\n\t\t\t.title2 {\r\n\t\t\t\tmargin-top: 32rpx;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #171717;\r\n\t\t\t}\r\n\r\n\t\t\t.money {\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.btn {\r\n\t\t\t\tmargin: 0 auto;\r\n\t\t\t\tmargin-top: 42rpx;\r\n\t\t\t\twidth: 688rpx;\r\n\t\t\t\theight: 98rpx;\r\n\t\t\t\tbackground: #2E80FE;\r\n\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\tline-height: 98rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.main {\r\n\t\t\tpadding: 40rpx odan30rpx;\r\n\r\n\t\t\t.main_item_already {\r\n\t\t\t\tpadding: 28rpx 36rpx;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tborder-radius: 24rpx;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\r\n\t\t\t\t.title {\r\n\t\t\t\t\tfont-size: 40rpx;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.ok {\r\n\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #E72427;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.no {\r\n\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\tmax-width: 500rpx;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.mid {\r\n\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\t.lef {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\twidth: 120rpx;\r\n\t\t\t\t\t\t\theight: 120rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\t\t\tmargin-left: 30rpx;\r\n\t\t\t\t\t\t\tmax-width: 350rpx;\r\n\t\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.bot {\r\n\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.shifu {\r\n\t\t\t\t\tmargin-top: 20rpx;\r\n\r\n\t\t\t\t\tscroll-view {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\twhite-space: nowrap;\r\n\r\n\t\t\t\t\t\t.shifu_item {\r\n\t\t\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\t\t\tmargin-right: 28rpx;\r\n\r\n\t\t\t\t\t\t\t.top {\r\n\t\t\t\t\t\t\t\tdisplay: flex;\r\n\r\n\t\t\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\t\t\twidth: 92rpx;\r\n\t\t\t\t\t\t\t\t\theight: 92rpx;\r\n\t\t\t\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t.info {\r\n\t\t\t\t\t\t\t\t\t.name {\r\n\t\t\t\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\t\t\tcolor: #E72427;\r\n\t\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.btnbox {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\tmargin-top: 52rpx;\r\n\r\n\t\t\t\t\t.btn {\r\n\t\t\t\t\t\twidth: 294rpx;\r\n\t\t\t\t\t\theight: 82rpx;\r\n\t\t\t\t\t\tline-height: 82rpx;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\tcolor: #2E80FE;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.can {\r\n\t\t\t\t\t\tborder: 2rpx solid #2E80FE;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.re {\r\n\t\t\t\t\t\tbackground: #2E80FE;\r\n\t\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// Loading status styles\r\n\t\t\t.loading-status {\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tpadding: 40rpx 0;\r\n\t\t\t\tcolor: #999999;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t}\r\n\r\n\t\t\t// Empty data styles\r\n\t\t\t.empty-data {\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tpadding: 200rpx 0;\r\n\t\t\t\tcolor: #999999;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_bao_list.vue?vue&type=style&index=0&id=0cdcf780&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_bao_list.vue?vue&type=style&index=0&id=0cdcf780&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755159803497\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}