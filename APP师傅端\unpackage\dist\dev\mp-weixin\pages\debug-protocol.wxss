@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.debug-page.data-v-0d67308b {
  min-height: 100vh;
  background: #f5f5f5;
}
.navbar.data-v-0d67308b {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 24rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}
.navbar .nav-left.data-v-0d67308b, .navbar .nav-right.data-v-0d67308b {
  width: 80rpx;
}
.navbar .nav-icon.data-v-0d67308b {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
}
.navbar .nav-title.data-v-0d67308b {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}
.test-section.data-v-0d67308b, .response-section.data-v-0d67308b {
  background: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 32rpx 24rpx;
}
.test-title.data-v-0d67308b, .response-title.data-v-0d67308b {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 20rpx;
}
.test-btn.data-v-0d67308b {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
}
.test-btn.data-v-0d67308b:disabled {
  background: #ccc;
}
.test-btn.data-v-0d67308b:active:not(:disabled) {
  opacity: 0.8;
}
.response-content.data-v-0d67308b {
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  max-height: 400rpx;
  overflow-y: auto;
}
.response-text.data-v-0d67308b {
  font-size: 24rpx;
  color: #333;
  line-height: 1.6;
  word-break: break-all;
  white-space: pre-wrap;
}
.log-section.data-v-0d67308b {
  background: #fff;
  margin: 0 20rpx 20rpx;
  border-radius: 12rpx;
  padding: 32rpx 24rpx;
}
.log-title.data-v-0d67308b {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 20rpx;
}
.log-content.data-v-0d67308b {
  max-height: 600rpx;
  overflow-y: auto;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
}
.log-item.data-v-0d67308b {
  display: block;
  font-size: 24rpx;
  line-height: 1.6;
  margin-bottom: 8rpx;
  word-break: break-all;
}
.log-item.info.data-v-0d67308b {
  color: #666;
}
.log-item.success.data-v-0d67308b {
  color: #52c41a;
}
.log-item.warning.data-v-0d67308b {
  color: #faad14;
}
.log-item.error.data-v-0d67308b {
  color: #f5222d;
}

