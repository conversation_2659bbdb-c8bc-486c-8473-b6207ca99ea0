{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/test-update.vue?6c7d", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/test-update.vue?b34e", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/test-update.vue?855d", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/test-update.vue?0656", "uni-app:///pages/test-update.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/test-update.vue?ee49", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/test-update.vue?7433"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "currentVersion", "isChecking", "logs", "onLoad", "methods", "goBack", "uni", "getCurrentVersion", "appUpdate", "addLog", "clearLogs", "testSilentCheck", "silent", "showLoading", "result", "test<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "testDirectAPI", "$api", "version", "platform", "response", "updateInfo"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA01B,CAAgB,02BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC6D92B;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MACAC;IACA;IAEA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAC;cAAA;gBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;MACA;QACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGAH;kBACAI;kBACAC;gBACA;cAAA;gBAHAC;gBAIA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGAP;kBACAI;kBACAC;gBACA;cAAA;gBAHAC;gBAIA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGAC;kBACAC;kBACAC;gBACA;cAAA;gBAHAC;gBAKA;gBAEA;kBACAC;kBACA;oBACA;oBACA;oBACA;kBACA;oBACA;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzLA;AAAA;AAAA;AAAA;AAAimD,CAAgB,qjDAAG,EAAC,C;;;;;;;;;;;ACArnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/test-update.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/test-update.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./test-update.vue?vue&type=template&id=59b4b70a&scoped=true&\"\nvar renderjs\nimport script from \"./test-update.vue?vue&type=script&lang=js&\"\nexport * from \"./test-update.vue?vue&type=script&lang=js&\"\nimport style0 from \"./test-update.vue?vue&type=style&index=0&id=59b4b70a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"59b4b70a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/test-update.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./test-update.vue?vue&type=template&id=59b4b70a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./test-update.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./test-update.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"test-update-page\">\n    <!-- 导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"nav-left\" @click=\"goBack\">\n        <text class=\"nav-icon\">‹</text>\n      </view>\n      <view class=\"nav-title\">更新测试</view>\n      <view class=\"nav-right\"></view>\n    </view>\n\n    <!-- 当前版本信息 -->\n    <view class=\"version-info\">\n      <text class=\"version-text\">当前版本: v{{ currentVersion }}</text>\n    </view>\n\n    <!-- 测试按钮 -->\n    <view class=\"test-buttons\">\n      <button \n        class=\"test-btn\" \n        @click=\"testSilentCheck\"\n        :disabled=\"isChecking\"\n      >\n        {{ isChecking ? '检查中...' : '测试静默检查' }}\n      </button>\n      \n      <button \n        class=\"test-btn\" \n        @click=\"testNormalCheck\"\n        :disabled=\"isChecking\"\n      >\n        {{ isChecking ? '检查中...' : '测试普通检查' }}\n      </button>\n      \n      <button \n        class=\"test-btn\" \n        @click=\"testDirectAPI\"\n        :disabled=\"isChecking\"\n      >\n        {{ isChecking ? '检查中...' : '测试直接API调用' }}\n      </button>\n    </view>\n\n    <!-- 日志显示 -->\n    <view class=\"log-section\">\n      <view class=\"log-title\">测试日志</view>\n      <view class=\"log-content\">\n        <text \n          class=\"log-item\" \n          v-for=\"(log, index) in logs\" \n          :key=\"index\"\n        >\n          {{ log }}\n        </text>\n      </view>\n      <button class=\"clear-btn\" @click=\"clearLogs\">清空日志</button>\n    </view>\n  </view>\n</template>\n\n<script>\nimport appUpdate from '@/utils/app-update.js'\nimport $api from '@/api/index.js'\n\nexport default {\n  name: 'TestUpdate',\n  data() {\n    return {\n      currentVersion: '1.0.0',\n      isChecking: false,\n      logs: []\n    }\n  },\n  onLoad() {\n    this.getCurrentVersion()\n    this.addLog('页面加载完成')\n  },\n  methods: {\n    /**\n     * 返回上一页\n     */\n    goBack() {\n      uni.navigateBack()\n    },\n\n    /**\n     * 获取当前版本号\n     */\n    async getCurrentVersion() {\n      this.currentVersion = await appUpdate.getCurrentVersion()\n      this.addLog(`获取当前版本: ${this.currentVersion}`)\n    },\n\n    /**\n     * 添加日志\n     */\n    addLog(message) {\n      const time = new Date().toLocaleTimeString()\n      this.logs.unshift(`[${time}] ${message}`)\n      if (this.logs.length > 50) {\n        this.logs = this.logs.slice(0, 50)\n      }\n    },\n\n    /**\n     * 清空日志\n     */\n    clearLogs() {\n      this.logs = []\n    },\n\n    /**\n     * 测试静默检查\n     */\n    async testSilentCheck() {\n      this.isChecking = true\n      this.addLog('开始测试静默检查...')\n      \n      try {\n        const result = await appUpdate.checkUpdate({\n          silent: true,\n          showLoading: false\n        })\n        this.addLog(`静默检查完成，结果: ${result ? '有更新' : '无更新'}`)\n      } catch (error) {\n        this.addLog(`静默检查失败: ${error.message}`)\n      } finally {\n        this.isChecking = false\n      }\n    },\n\n    /**\n     * 测试普通检查\n     */\n    async testNormalCheck() {\n      this.isChecking = true\n      this.addLog('开始测试普通检查...')\n      \n      try {\n        const result = await appUpdate.checkUpdate({\n          silent: false,\n          showLoading: true\n        })\n        this.addLog(`普通检查完成，结果: ${result ? '有更新' : '无更新'}`)\n      } catch (error) {\n        this.addLog(`普通检查失败: ${error.message}`)\n      } finally {\n        this.isChecking = false\n      }\n    },\n\n    /**\n     * 测试直接API调用\n     */\n    async testDirectAPI() {\n      this.isChecking = true\n      this.addLog('开始测试直接API调用...')\n      \n      try {\n        const response = await $api.user.checkAppVersion({\n          version: this.currentVersion,\n          platform: 1  // 师傅端平台类型\n        })\n        \n        this.addLog(`API调用成功: ${JSON.stringify(response)}`)\n        \n        if (response.code === '200' && response.data) {\n          const updateInfo = response.data\n          if (updateInfo.needUpdate) {\n            this.addLog(`发现新版本: v${updateInfo.latestVersion}`)\n            this.addLog(`更新描述: ${updateInfo.description}`)\n            this.addLog(`是否强制更新: ${updateInfo.forceUpdate}`)\n          } else {\n            this.addLog('已是最新版本')\n          }\n        } else {\n          this.addLog(`API返回错误: ${response.msg}`)\n        }\n      } catch (error) {\n        this.addLog(`API调用失败: ${error.message}`)\n      } finally {\n        this.isChecking = false\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.test-update-page {\n  min-height: 100vh;\n  background: #f5f5f5;\n}\n\n.navbar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 88rpx;\n  padding: 0 24rpx;\n  background: #fff;\n  border-bottom: 1rpx solid #eee;\n  \n  .nav-left, .nav-right {\n    width: 80rpx;\n  }\n  \n  .nav-icon {\n    font-size: 36rpx;\n    color: #333;\n    font-weight: bold;\n  }\n  \n  .nav-title {\n    font-size: 32rpx;\n    color: #333;\n    font-weight: 500;\n  }\n}\n\n.version-info {\n  padding: 40rpx 24rpx;\n  background: #fff;\n  margin-bottom: 20rpx;\n  text-align: center;\n  \n  .version-text {\n    font-size: 32rpx;\n    color: #333;\n    font-weight: 500;\n  }\n}\n\n.test-buttons {\n  padding: 0 24rpx;\n  margin-bottom: 20rpx;\n  \n  .test-btn {\n    width: 100%;\n    height: 88rpx;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    color: #fff;\n    border: none;\n    border-radius: 44rpx;\n    font-size: 32rpx;\n    font-weight: 500;\n    margin-bottom: 20rpx;\n    \n    &:disabled {\n      background: #ccc;\n    }\n    \n    &:active:not(:disabled) {\n      opacity: 0.8;\n    }\n  }\n}\n\n.log-section {\n  background: #fff;\n  margin: 0 24rpx;\n  border-radius: 12rpx;\n  padding: 32rpx 24rpx;\n  \n  .log-title {\n    font-size: 30rpx;\n    color: #333;\n    font-weight: 600;\n    margin-bottom: 20rpx;\n  }\n  \n  .log-content {\n    max-height: 600rpx;\n    overflow-y: auto;\n    background: #f8f8f8;\n    border-radius: 8rpx;\n    padding: 20rpx;\n    margin-bottom: 20rpx;\n    \n    .log-item {\n      display: block;\n      font-size: 24rpx;\n      color: #666;\n      line-height: 1.6;\n      margin-bottom: 8rpx;\n      word-break: break-all;\n    }\n  }\n  \n  .clear-btn {\n    width: 100%;\n    height: 60rpx;\n    background: #f0f0f0;\n    color: #666;\n    border: none;\n    border-radius: 30rpx;\n    font-size: 28rpx;\n  }\n}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./test-update.vue?vue&type=style&index=0&id=59b4b70a&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./test-update.vue?vue&type=style&index=0&id=59b4b70a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755159810194\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}