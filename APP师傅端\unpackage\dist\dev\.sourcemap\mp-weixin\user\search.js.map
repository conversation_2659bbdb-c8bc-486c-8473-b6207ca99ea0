{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/search.vue?f5ae", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/search.vue?e00f", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/search.vue?ac7e", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/search.vue?fb55", "uni-app:///user/search.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/search.vue?16c2", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/search.vue?9943"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "list", "searchList", "value", "methods", "debounce", "args", "timer", "fn", "handleSearch", "item", "handleInput", "chooseOne", "uni", "url", "getList", "console", "res", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAq1B,CAAgB,q2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCcz2B;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;QAAA;QAAA;UAAAC;QAAA;QACA;QACAC;UACAC;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;UAAA,OACAC;QAAA,EACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACAC;QACAC;MACA;IACA;IACAC;MAAA;MACA;MACA;QACAC;QACA;MACA;MACA;QACA;QACAC;UACA;YACAP;cACA;gBACA;cACA;YACA;UACA;QACA;QACAM;MACA;QACAA;MACA;IACA;EACA;EACAE;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5EA;AAAA;AAAA;AAAA;AAA4lD,CAAgB,gjDAAG,EAAC,C;;;;;;;;;;;ACAhnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/search.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/search.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./search.vue?vue&type=template&id=18f31364&scoped=true&\"\nvar renderjs\nimport script from \"./search.vue?vue&type=script&lang=js&\"\nexport * from \"./search.vue?vue&type=script&lang=js&\"\nimport style0 from \"./search.vue?vue&type=style&index=0&id=18f31364&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"18f31364\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/search.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./search.vue?vue&type=template&id=18f31364&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./search.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./search.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<view class=\"search_position\">\n\t\t\t<uni-icons type=\"search\" size=\"20\" color=\"#ADADAD\"></uni-icons>\n\t\t\t<input type=\"text\" placeholder=\"空调维修\" v-model=\"value\" @input=\"handleInput\">\n\t\t\t<view class=\"btn\" @click=\"handleSearch\">搜索</view>\n\t\t</view>\n\t\t<view class=\"search_main\">\n\t\t\t<view class=\"search_item\" v-for=\"(item, index) in searchList\" :key=\"index\" @click=\"chooseOne(item)\">{{ item.title }}</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tlist: [],\n\t\t\tsearchList: [],\n\t\t\tvalue: ''\n\t\t}\n\t},\n\tmethods: {\n\t\t// Custom debounce function\n\t\tdebounce(fn, delay) {\n\t\t\tlet timer = null;\n\t\t\treturn function (...args) {\n\t\t\t\tif (timer) clearTimeout(timer);\n\t\t\t\ttimer = setTimeout(() => {\n\t\t\t\t\tfn.apply(this, args);\n\t\t\t\t}, delay);\n\t\t\t};\n\t\t},\n\t\thandleSearch() {\n\t\t\tthis.searchList = [];\n\t\t\tif (this.value.trim()) {\n\t\t\t\tthis.searchList = this.list.filter(item => \n\t\t\t\t\titem.title.toLowerCase().includes(this.value.toLowerCase())\n\t\t\t\t);\n\t\t\t}\n\t\t},\n\t\thandleInput() {\n\t\t\t// Debounced search to reduce frequent updates\n\t\t\tthis.debounce(this.handleSearch, 300).call(this);\n\t\t},\n\t\tchooseOne(item) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/user/commodity_details?id=${item.id}`\n\t\t\t});\n\t\t},\n\t\tgetList() {\n\t\t\tlet city_id = uni.getStorageSync('city');\n\t\t\tif (!city_id?.position) {\n\t\t\t\tconsole.error('City ID not found');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.$api.service.serviceCate(city_id.position).then(res => {\n\t\t\t\tthis.list = [];\n\t\t\t\tres.forEach(item => {\n\t\t\t\t\tif (item.children) {\n\t\t\t\t\t\titem.children.forEach(e => {\n\t\t\t\t\t\t\tif (e.serviceList) {\n\t\t\t\t\t\t\t\tthis.list = [...this.list, ...e.serviceList];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tconsole.log('List:', this.list);\n\t\t\t}).catch(err => {\n\t\t\t\tconsole.error('API error:', err);\n\t\t\t});\n\t\t}\n\t},\n\tonLoad() {\n\t\tthis.getList();\n\t}\n}\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n\t.search_position {\n\t\tmargin: 0 auto;\n\t\tmargin-top: 24rpx;\n\t\twidth: 690rpx;\n\t\theight: 72rpx;\n\t\tbackground: #F1F1F1;\n\t\tborder-radius: 36rpx 36rpx 36rpx 36rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding-left: 40rpx;\n\t\tpadding-right: 20rpx;\n\t\tposition: relative;\n\t\t\n\t\tinput {\n\t\t\tmargin-left: 22rpx;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 400;\n\t\t\tcolor: #ADADAD;\n\t\t\twidth: 260rpx;\n\t\t}\n\t\n\t\t.btn {\n\t\t\twidth: 112rpx;\n\t\t\theight: 56rpx;\n\t\t\tbackground: #2E80FE;\n\t\t\tborder-radius: 28rpx 28rpx 28rpx 28rpx;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 400 discriminators;\n\t\t\tcolor: #FFFFFF;\n\t\t\tline-height: 56rpx;\n\t\t\ttext-align: center;\n\t\t\tposition: absolute;\n\t\t\tright: 20rpx;\n\t\t}\n\t}\n\t.search_main {\n\t\t.search_item {\n\t\t\theight: 120rpx;\n\t\t\tborder-bottom: 2rpx solid #E9E9E9;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 400;\n\t\t\tcolor: #333333;\n\t\t\tpadding: 0 30rpx;\n\t\t}\n\t}\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./search.vue?vue&type=style&index=0&id=18f31364&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./search.vue?vue&type=style&index=0&id=18f31364&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755159809872\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}