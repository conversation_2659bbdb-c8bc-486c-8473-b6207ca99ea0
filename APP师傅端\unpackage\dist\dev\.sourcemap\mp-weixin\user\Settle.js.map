{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/Settle.vue?c0b3", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/Settle.vue?8d2e", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/Settle.vue?bcf1", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/Settle.vue?2b46", "uni-app:///user/Settle.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/Settle.vue?a17e", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/Settle.vue?9d23"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "Upload", "data", "flag", "showSh", "shInfo", "title", "contentMoney", "show", "arr", "text", "color", "form", "userId", "<PERSON><PERSON><PERSON>", "sex", "mobile", "workTime", "city", "cityId", "lng", "lat", "address", "idCode", "id_card1", "id_card2", "self_img", "showMoney", "entryNotice", "showCity", "loading", "columnsCity", "methods", "getCurrentPlatform", "handleAppWechatPay", "console", "uni", "orderInfo", "appid", "noncestr", "package", "partnerid", "prepayid", "timestamp", "sign", "icon", "duration", "subForm", "url", "handleMiniProgramPay", "timeStamp", "nonceStr", "signType", "paySign", "appId", "success", "fail", "confirmCity", "map", "join", "<PERSON><PERSON><PERSON><PERSON>", "columnIndex", "e", "index", "picker", "shDetail", "imgUpload", "imgtype", "getcon", "<PERSON><PERSON><PERSON>", "platform", "res", "obj", "packageStr", "submit", "cancelModel", "confirmModel", "getcity", "seeInfo", "path", "onLoad", "city_id", "onShow", "onUnload"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,4RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9DA;AAAA;AAAA;AAAA;AAAq1B,CAAgB,q2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4Jz2B;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,MACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA,IACA;QAAAD;QAAAC;MAAA,EACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAb;QACAc;QAAA;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MAKA;MAKA;IACA;IAEA;IACAC;MAAA;QAAA;MACAC;MACAC;QACA;QACAC;MAAA,mEACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,kEACA;QACAT;QACAC;UACA9B;UACAuC;UACAC;QACA;QACA;QACAC;QACA;QACA;QACA;QAEAA;UAAA;QAAA;QACAA;UAAA;QAAA;QAEA;UACAZ;UACA;YACAC;cACAY;YACA;UACA;QACA;MACA,+DACA;QACAb;QACA;UACAC;YACA9B;YACAuC;UACA;QACA;UACAT;YACA9B;YACAuC;UACA;QACA;MACA,yBACA;IACA;IAEA;IACAI;MAAA;MACA;QACAC;QAAA;QACAC;QACAX;QACAY;QACAC;MACA;MACAlB;MACAC;QACA;QACAc;QACAC;QACAX;QACAC;QACAW;QACAC;QACAC;QACAC;UACA;UACApB;UACAC;YACA9B;YACAuC;YACAC;UACA;UACA;UACAC;UACA;UACA;UACA;UAEAA;YAAA;UAAA;UACAA;YAAA;UAAA;UAEA;YACAZ;YACA;cACAC;gBACAY;cACA;YACA;UACA;QACA;QACAQ;UACA;UACArB;UACAA;UACA;YACAC;cACA9B;cACAuC;YACA;UACA;YACAT;cACA9B;cACAuC;YACA;UACA;UACAV;UACAC;YACA9B;YACAuC;UACA;QACA;MACA;IACA;IAEAY;MAAA;MACA,6BACAC;QACA;UACA;QACA;UACA;QACA;MACA,GACAC;MACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;IACA;IACAC;MAAA;MACA,IACAC,cAGAC,EAHAD;QACAE,QAEAD,EAFAC;QAAA,YAEAD,EADAE;QAAAA;MAEA;QACA;UACAA;UACA;UACA;YACAA;YACA;UACA;QACA;MACA;QACA;UACAA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA/B;MACA;QAAAgC;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAEA;gBACAC;gBACAnC;gBAAA;gBAAA,OAEA;cAAA;gBAAAoC;gBACAC;gBACArC;gBACAsC;gBACAtC;gBACAA;gBACAA;gBACAA;gBACAA;gBACAA;;gBAEA;gBACA;kBACA;kBACAA;kBACA;gBACA;kBACA;kBACAA;kBACA;gBACA;kBACA;kBACAA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;kBACA9B;kBACAuC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA6B;MACA;QACA;UACAtC;YACAS;YACAvC;UACA;UACA;QACA;MACA;MACA;QACA8B;UACAS;UACAvC;QACA;QACA;MACA;MACA;MACA;QACA8B;UACAS;UACAvC;QACA;QACA;MACA;MACA;MACA;QACA8B;UACAS;UACAvC;UACAwC;QACA;QACA;MACA;MACA;IACA;IACA6B;MACAvC;IACA;IACAwC;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;UACA;UACA;YACA;YACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;UACA,4DACA,cACAP;YACA/C;cAAAuD;YAAA;YACAtD;cAAAsD;YAAA;YACArD;cAAA;gBAAAqD;cAAA;YAAA;UAAA,EACA;QACA;UACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;MAAAC;MAAA7D;MAAAC;IACA;IACA;IACA;IACA;IACA;EACA;EACA6D;IAAA;IACA9C;MACA;QACA;MACA;IACA;EACA;EACA+C;IACA/C;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/jBA;AAAA;AAAA;AAAA;AAA4lD,CAAgB,gjDAAG,EAAC,C;;;;;;;;;;;ACAhnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/Settle.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/Settle.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./Settle.vue?vue&type=template&id=cde716de&scoped=true&\"\nvar renderjs\nimport script from \"./Settle.vue?vue&type=script&lang=js&\"\nexport * from \"./Settle.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Settle.vue?vue&type=style&index=0&id=cde716de&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"cde716de\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/Settle.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Settle.vue?vue&type=template&id=cde716de&scoped=true&\"", "var components\ntry {\n  components = {\n    uPicker: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-picker/u-picker\" */ \"uview-ui/components/u-picker/u-picker.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-modal/u-modal\" */ \"uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n    uRadioGroup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-radio-group/u-radio-group\" */ \"uview-ui/components/u-radio-group/u-radio-group.vue\"\n      )\n    },\n    uRadio: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-radio/u-radio\" */ \"uview-ui/components/u-radio/u-radio.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showCity = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.showMoney = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.showSh = false\n    }\n    _vm.e3 = function ($event) {\n      _vm.showCity = true\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Settle.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Settle.vue?vue&type=script&lang=js&\"", "<template>\n    <view class=\"page\">\n        <u-picker\n            v-if=\"flag\"\n            :show=\"showCity\"\n            ref=\"uPicker\"\n            :loading=\"loading\"\n            :columns=\"columnsCity\"\n            @change=\"changeHandler\"\n            keyName=\"title\"\n            @cancel=\"showCity = false\"\n            @confirm=\"confirmCity\"\n        ></u-picker>\n        <u-modal\n            :show=\"showMoney\"\n            :showCancelButton=\"true\"\n            cancelText=\"再想想\"\n            @confirm=\"confirmMoney\"\n            @cancel=\"showMoney = false\"\n        >\n            <view class=\"slot-content\">\n                <rich-text :nodes=\"contentMoney\"></rich-text>\n            </view>\n        </u-modal>\n        <u-modal\n            :show=\"show\"\n            :title=\"title\"\n            :showCancelButton=\"true\"\n            confirmText=\"同意\"\n            cancelText=\"不同意\"\n            @confirm=\"confirmModel\"\n            @cancel=\"cancelModel\"\n        >\n            <view class=\"slot-content\">\n                <rich-text :nodes=\"entryNotice\"></rich-text>\n            </view>\n        </u-modal>\n        <u-modal\n            v-if=\"shInfo.status == 4\"\n            :show=\"showSh\"\n            title=\"驳回原因\"\n            confirmText=\"确定\"\n            @confirm=\"showSh = false\"\n            :content=\"shInfo.sh_text\"\n        ></u-modal>\n        <view\n            class=\"header\"\n            v-if=\"shInfo.status\"\n            :style=\"'color:' + arr[shInfo.status - 1].color\"\n            @click=\"shDetail\"\n        >\n            {{ arr[shInfo.status - 1].text }}\n        </view>\n        <view class=\"main\">\n            <view class=\"main_item\">\n                <view class=\"title\"><span>*</span>姓名</view>\n                <input type=\"text\" v-model=\"form.coachName\" placeholder=\"请输入姓名\" />\n            </view>\n            <view class=\"main_item\">\n                <view class=\"title\"><span>*</span>手机号</view>\n                <input type=\"text\" v-model=\"form.mobile\" placeholder=\"请输入手机号\" />\n            </view>\n            <view class=\"main_item\">\n                <view class=\"title\"><span>*</span>性别</view>\n                <u-radio-group v-model=\"form.sex\" placement=\"row\">\n                    <u-radio :customStyle=\"{ marginRight: '20px' }\" label=\"男\" :name=\"0\"></u-radio>\n                    <u-radio label=\"女\" :name=\"1\"></u-radio>\n                </u-radio-group>\n            </view>\n            <view class=\"main_item\">\n                <view class=\"title\"><span>*</span>从业年份</view>\n                <input type=\"text\" v-model=\"form.workTime\" placeholder=\"请输入从业年份\" />\n            </view>\n            <view class=\"main_item\">\n                <view class=\"title\"><span>*</span>选择区域</view>\n                <input\n                    type=\"text\"\n                    v-model=\"form.city\"\n                    placeholder=\"请选择代理区域\"\n                    disabled\n                    @click=\"showCity = true\"\n                />\n            </view>\n            <view class=\"main_item\">\n                <view class=\"title\"><span>*</span>详细地址</view>\n                <input type=\"text\" v-model=\"form.address\" placeholder=\"请输入详细地址\" />\n            </view>\n            <view class=\"main_item\">\n                <view class=\"title\"><span>*</span>自我介绍(非必填)</view>\n                <input type=\"text\" v-model=\"form.text\" placeholder=\"请输入自我介绍\" />\n            </view>\n            <view class=\"main_item\">\n                <view class=\"title\"><span>*</span>身份证号</view>\n                <input type=\"text\" v-model=\"form.idCode\" placeholder=\"请输入身份证号\" />\n            </view>\n            <view class=\"main_item\">\n                <view class=\"title\"><span>*</span>上传身份证照片</view>\n                <view class=\"card\">\n                    <view class=\"card_item\">\n                        <view class=\"top\">\n                            <view class=\"das\">\n                                <view class=\"up\">\n                                    <upload\n                                        @upload=\"imgUpload\"\n                                        @del=\"imgUpload\"\n                                        :imagelist=\"form.id_card1\"\n                                        imgtype=\"id_card1\"\n                                        imgclass=\"id_card_box\"\n                                        text=\"身份证人像面\"\n                                        :imgsize=\"1\"\n                                    ></upload>\n                                </view>\n                            </view>\n                        </view>\n                        <view class=\"bottom\">拍摄人像面</view>\n                    </view>\n                    <view class=\"card_item\">\n                        <view class=\"top\">\n                            <view class=\"das\">\n                                <view class=\"up\">\n                                    <upload\n                                        @upload=\"imgUpload\"\n                                        @del=\"imgUpload\"\n                                        :imagelist=\"form.id_card2\"\n                                        imgtype=\"id_card2\"\n                                        imgclass=\"id_card_box\"\n                                        text=\"身份证国徽面\"\n                                        :imgsize=\"1\"\n                                    ></upload>\n                                </view>\n                            </view>\n                        </view>\n                        <view class=\"bottom\">拍摄国徽面</view>\n                    </view>\n                </view>\n            </view>\n            <view class=\"main_item\">\n                <view class=\"title\"><span>*</span>上传形象照片</view>\n                <upload\n                    @upload=\"imgUpload\"\n                    @del=\"imgUpload\"\n                    :imagelist=\"form.self_img\"\n                    imgtype=\"self_img\"\n                    imgclass=\"\"\n                    text=\"形象照片\"\n                    :imgsize=\"3\"\n                ></upload>\n            </view>\n        </view>\n        <view class=\"footer\" v-if=\"!shInfo.status\">\n            <view class=\"btn\" @click=\"submit\">立即提交</view>\n        </view>\n    </view>\n</template>\n\n<script>\nimport Upload from '@/components/upload.vue'; // Adjust path to your upload.vue\n\nexport default {\n    components: {\n        Upload,\n    },\n    data() {\n        return {\n            flag: false,\n            showSh: false,\n            shInfo: {},\n            title: '入驻须知',\n            contentMoney: '',\n            show: false,\n            arr: [\n                { text: '信息审核中，请稍作等待', color: '#FE921B' },\n                { text: '审核成功', color: '#07C160' },\n                {},\n                { text: '审核失败>', color: '#E72427' },\n            ],\n            form: {\n                userId: '',\n                coachName: '',\n                sex: 0,\n                mobile: '',\n                workTime: '',\n                city: '',\n                cityId: [],\n                lng: '',\n                lat: '',\n                address: '',\n                idCode: '',\n                text: '',\n                id_card1: [], // Changed from idCard to id_card1\n                id_card2: [],\n                self_img: [],\n            },\n            showMoney: false,\n            entryNotice: '',\n            showCity: false,\n            loading: false,\n            columnsCity: [[], [], []],\n        };\n    },\n    methods: {\n\t\t// 检查当前平台\n\t\tgetCurrentPlatform() {\n\t\t\t// #ifdef APP-PLUS\n\t\t\treturn 'app-plus';\n\t\t\t// #endif\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\treturn 'mp-weixin';\n\t\t\t// #endif\n\t\t\t// #ifdef H5\n\t\t\treturn 'h5';\n\t\t\t// #endif\n\t\t\treturn 'unknown';\n\t\t},\n\n\t\t// APP微信支付处理\n\t\thandleAppWechatPay(obj) {\n\t\t\tconsole.log(111)\n\t\t\tuni.requestPayment({\n\t\t\t\t\"provider\": \"wxpay\",\n\t\t\t\t    orderInfo: 'orderInfo',\n\t\t\t\torderInfo: {\n\t\t\t\t\tappid: obj.appId,\n\t\t\t\t\tnoncestr: obj.nonceStr,\n\t\t\t\t\tpackage: 'Sign=WXPay',\n\t\t\t\t\tpartnerid: obj.partnerId,\n\t\t\t\t\tprepayid: obj.prepayId,\n\t\t\t\t\ttimestamp: String(obj.timestamp),\n\t\t\t\t\tsign: obj.sign\n\t\t\t\t},\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconsole.log('APP微信支付成功', res);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '支付成功',\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\tduration: 1500,\n\t\t\t\t\t});\n\t\t\t\t\tlet subForm = JSON.parse(JSON.stringify(this.form));\n\t\t\t\t\tsubForm.id_card = subForm.id_card1.concat(subForm.id_card2);\n\t\t\t\t\tdelete subForm.id_card1;\n\t\t\t\t\tdelete subForm.id_card2;\n\t\t\t\t\tdelete subForm.city;\n\n\t\t\t\t\tsubForm.id_card = subForm.id_card.map((item) => item.path);\n\t\t\t\t\tsubForm.self_img = subForm.self_img.map((item) => item.path);\n\n\t\t\t\t\tthis.$api.service.masterEnter(subForm).then(res2 => {\n\t\t\t\t\t\tconsole.log(res2)\n\t\t\t\t\t\tif (res2) {\n\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\turl: '/pages/apply_over',\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('APP微信支付失败:', err);\n\t\t\t\t\tif (err.errMsg && err.errMsg.includes('cancel')) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '您已取消支付',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '支付失败，请稍后重试',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 微信小程序支付处理（保持原有逻辑）\n\t\thandleMiniProgramPay(obj) {\n\t\t\tconst paymentParams = {\n\t\t\t\ttimeStamp: String(obj.timestamp), // 一定要是 string\n\t\t\t\tnonceStr: obj.nonceStr,\n\t\t\t\tpackage: \"prepay_id=\" + obj.prepayId,\n\t\t\t\tsignType: 'MD5',\n\t\t\t\tpaySign: obj.sign\n\t\t\t};\n\t\t\tconsole.log(JSON.stringify(paymentParams));\n\t\t\tuni.requestPayment({\n\t\t\t\t\"provider\": 'wxpay',\n\t\t\t\ttimeStamp: String(obj.timestamp),\n\t\t\t\tnonceStr: obj.nonceStr,\n\t\t\t\tpackage: \"prepay_id=\" + obj.prepayId,\n\t\t\t\tpartnerid: obj.partnerId,\n\t\t\t\tsignType: \"MD5\",\n\t\t\t\tpaySign: obj.sign,\n\t\t\t\tappId: obj.appId,\n\t\t\t\tsuccess: (res1) => {\n\t\t\t\t\t// 支付成功回调\n\t\t\t\t\tconsole.log('支付成功', res1);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '支付成功',\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\tduration: 1500,\n\t\t\t\t\t});\n\t\t\t\t\tlet subForm = JSON.parse(JSON.stringify(this.form));\n\t\t\t\t\tsubForm.id_card = subForm.id_card1.concat(subForm.id_card2);\n\t\t\t\t\tdelete subForm.id_card1;\n\t\t\t\t\tdelete subForm.id_card2;\n\t\t\t\t\tdelete subForm.city;\n\n\t\t\t\t\tsubForm.id_card = subForm.id_card.map((item) => item.path);\n\t\t\t\t\tsubForm.self_img = subForm.self_img.map((item) => item.path);\n\n\t\t\t\t\tthis.$api.service.masterEnter(subForm).then(res2 => {\n\t\t\t\t\t\tconsole.log(res2)\n\t\t\t\t\t\tif (res2) {\n\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\turl: '/pages/apply_over',\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\t// 支付失败回调\n\t\t\t\t\tconsole.error('requestPayment fail object:', err);\n\t\t\t\t\tconsole.error('requestPayment fail JSON:', JSON.stringify(err));\n\t\t\t\t\tif (err.errMsg.includes('fail cancel')) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '您已取消支付',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '支付失败，请稍后重试',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\tconsole.error('支付失败', err);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '支付失败请检查网络',\n\t\t\t\t\t\ticon: 'error'\n\t\t\t\t\t})\n\t\t\t\t},\n\t\t\t})\n\t\t},\n\n        confirmCity(Array) {\n            this.form.city = Array.value\n                .map((item, index) => {\n                    if (item == undefined) {\n                        return this.columnsCity[index][0].title;\n                    } else {\n                        return item.title;\n                    }\n                })\n                .join('-');\n            this.form.cityId = Array.value.map((e, j) => {\n                if (e == undefined) {\n                    return this.columnsCity[j][0].id;\n                } else {\n                    return e.id;\n                }\n            });\n            this.showCity = false;\n        },\n        changeHandler(e) {\n            const {\n                columnIndex,\n                index,\n                picker = this.$refs.uPicker,\n            } = e;\n            if (columnIndex === 0) {\n                this.$api.service.getCity(this.columnsCity[0][index].id).then((res) => {\n                    picker.setColumnValues(1, res);\n                    this.columnsCity[1] = res;\n                    this.$api.service.getCity(res[0].id).then((res1) => {\n                        picker.setColumnValues(2, res1);\n                        this.columnsCity[2] = res1;\n                    });\n                });\n            } else if (columnIndex === 1) {\n                this.$api.service.getCity(this.columnsCity[1][index].id).then((res) => {\n                    picker.setColumnValues(2, res);\n                    this.columnsCity[2] = res;\n                });\n            }\n        },\n        shDetail() {\n            if (this.shInfo.status != 4) return;\n            this.showSh = true;\n        },\n        imgUpload(e) {\n            console.log('imgUpload event:', e);\n            const { imagelist, imgtype } = e;\n            this.$set(this.form, imgtype, imagelist); // Use $set for reactivity\n        },\n        getcon() {\n            this.$api.service.getConfig().then((res) => {\n                this.contentMoney = `入驻需交<span style=\"color:red;\">${res.data.cashPledge}元</span>押金，是否申请`;\n                this.entryNotice = res.entryNotice;\n            });\n        },\n        // async confirmMoney() {\n        //     this.showMoney = false;\n        //     try {\n        //         const res = await this.$api.service.masterPayY();\n        //         const obj = res.pay_list;\n        //         uni.requestPayment({\n        //             provider: 'wxpay',\n        //             timeStamp: obj.timeStamp,\n        //             nonceStr: obj.nonceStr,\n        //             package: obj.package,\n        //             signType: obj.signType,\n        //             paySign: obj.paySign,\n        //             success: async () => {\n        //                 uni.showToast({\n        //                     title: '支付成功',\n        //                     icon: 'success',\n        //                     duration: 1500,\n        //                 });\n        //                 let subForm = JSON.parse(JSON.stringify(this.form));\n        //                 subForm.id_card = subForm.id_card1.concat(subForm.id_card2);\n        //                 delete subForm.id_card1;\n        //                 delete subForm.id_card2;\n        //                 delete subForm.city;\n\n        //                 subForm.id_card = subForm.id_card.map((item) => item.path);\n        //                 subForm.self_img = subForm.self_img.map((item) => item.path);\n\n        //                 const res2 = await this.$api.service.masterIn(subForm);\n        //                 if (res2) {\n        //                     uni.navigateTo({\n        //                         url: '/pages/apply_over',\n        //                     });\n        //                 }\n        //             },\n        //             fail: (err) => {\n        //                 uni.showToast({\n        //                     title: '支付失败请检查网络',\n        //                     icon: 'error',\n        //                 });\n        //             },\n        //         });\n        //     } catch (error) {\n        //         uni.showToast({\n        //             title: error.message || '支付失败，请重试',\n        //             icon: 'error',\n        //         });\n        //     }\n        // },\n\t\tasync confirmMoney() {\n\t\t    this.showMoney = false;\n\t\t    try {\n\t\t\t\t// 获取当前平台\n\t\t\t\tconst platform = this.getCurrentPlatform();\n\t\t\t\tconsole.log('当前平台:', platform);\n\n\t\t        const res = await this.$api.service.masterPayY();\n\t\t        const obj = res.pay_list;\n\t\t\t\tconsole.log(res)\n\t\t\t\tlet packageStr = \"prepay_id=\" + obj.prepayId;\n\t\t\t\tconsole.log(String(packageStr))\n\t\t\t\tconsole.log(obj.nonceStr)\n\t\t\t\tconsole.log(packageStr)\n\t\t\t\tconsole.log(obj.nonceStr)\n\t\t\t\tconsole.log(String(obj.timestamp))\n\t\t\t\tconsole.log(obj.sign)\n\n\t\t\t\t// 根据平台选择不同的支付方式\n\t\t\t\tif (platform === 'app-plus') {\n\t\t\t\t\t// APP环境使用微信支付\n\t\t\t\t\tconsole.log('APP环境，使用微信支付');\n\t\t\t\t\tthis.handleAppWechatPay(obj);\n\t\t\t\t} else if (platform === 'mp-weixin') {\n\t\t\t\t\t// 微信小程序环境保持原有逻辑\n\t\t\t\t\tconsole.log('微信小程序环境，使用小程序支付');\n\t\t\t\t\tthis.handleMiniProgramPay(obj);\n\t\t\t\t} else {\n\t\t\t\t\t// 其他环境（H5等）\n\t\t\t\t\tconsole.log('其他环境，使用默认支付方式');\n\t\t\t\t\tthis.handleMiniProgramPay(obj);\n\t\t\t\t}\n\t\t    } catch (error) {\n\t\t        uni.showToast({\n\t\t            title: error.message || '支付失败，请重试',\n\t\t            icon: 'error',\n\t\t        });\n\t\t    }\n\t\t},\n        submit() {\n            for (let key in this.form) {\n                if (this.form[key] === '' || (Array.isArray(this.form[key]) && this.form[key].length === 0)) {\n                    uni.showToast({\n                        icon: 'none',\n                        title: '请填写完整提交',\n                    });\n                    return;\n                }\n            }\n            if (this.form.id_card1.length === 0 || this.form.id_card2.length === 0 || this.form.self_img.length === 0) {\n                uni.showToast({\n                    icon: 'none',\n                    title: '请上传所需图片',\n                });\n                return;\n            }\n            let p = /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/;\n            if (!p.test(this.form.idCode)) {\n                uni.showToast({\n                    icon: 'none',\n                    title: '请填写正确的身份证号',\n                });\n                return;\n            }\n            let phoneReg = /^1[3456789]\\d{9}$/;\n            if (!phoneReg.test(this.form.mobile)) {\n                uni.showToast({\n                    icon: 'none',\n                    title: '请填写正确的手机号',\n                    duration: 1000,\n                });\n                return;\n            }\n            this.showMoney = true;\n        },\n        cancelModel() {\n            uni.navigateBack();\n        },\n        confirmModel() {\n            this.show = false;\n        },\n        getcity(e) {\n            this.$api.service.getCity(e).then((res) => {\n                this.columnsCity[0] = res;\n                this.$api.service.getCity(res[0].id).then((res1) => {\n                    this.columnsCity[1] = res1;\n                    this.$api.service.getCity(res1[0].id).then((res2) => {\n                        this.columnsCity[2] = res2;\n                        this.flag = true;\n                    });\n                });\n            });\n        },\n        seeInfo() {\n            this.$api.service.masterSee().then((res) => {\n                if (res && Object.keys(res).length !== 0) {\n                    this.shInfo = res;\n                    this.form = {\n                        ...this.form,\n                        ...res,\n                        id_card1: res.id_card && res.id_card[0] ? [{ path: res.id_card[0] }] : [],\n                        id_card2: res.id_card && res.id_card[1] ? [{ path: res.id_card[1] }] : [],\n                        self_img: res.self_img ? res.self_img.map((item) => ({ path: item })) : [],\n                    };\n                } else {\n                    this.show = true;\n                }\n            });\n        },\n    },\n    onLoad() {\n        this.getcon();\n        this.form.userId = this.$store.state.user.userInfo.id;\n        const { city_id, lng, lat } = this.$store.state.user.userInfo;\n        this.form.cityId = city_id;\n        this.form.lng = lng;\n        this.form.lat = lat;\n        this.getcity(0);\n        this.seeInfo();\n    },\n    onShow() {\n        uni.$on('getShInfo', (data) => {\n            if (data) {\n                this.seeInfo();\n            }\n        });\n    },\n    onUnload() {\n        uni.$off('getShInfo');\n    },\n};\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n    padding-bottom: 200rpx;\n\n    .header {\n        width: 750rpx;\n        height: 58rpx;\n        background: #fff7f1;\n        line-height: 58rpx;\n        text-align: center;\n        font-size: 28rpx;\n        font-weight: 400;\n    }\n\n    .main {\n        padding: 40rpx 30rpx;\n\n        .main_item {\n            margin-bottom: 20rpx;\n\n            .title {\n                margin-bottom: 20rpx;\n                font-size: 28rpx;\n                font-weight: 400;\n                color: #333333;\n\n                span {\n                    color: #e72427;\n                }\n            }\n\n            input {\n                width: 690rpx;\n                height: 110rpx;\n                background: #f8f8f8;\n                font-size: 28rpx;\n                font-weight: 400;\n                line-height: 110rpx;\n                padding: 0 40rpx;\n                box-sizing: border-box;\n            }\n\n            .card {\n                display: flex;\n                align-items: center;\n                justify-content: space-between;\n\n                .card_item {\n                    width: 332rpx;\n                    height: 332rpx;\n                    background: #f2fafe;\n                    border-radius: 16rpx;\n                    overflow: hidden;\n\n                    .top {\n                        height: 266rpx;\n                        width: 100%;\n                        padding-top: 40rpx;\n\n                        .das {\n                            margin: 0 auto;\n                            width: 266rpx;\n                            height: 180rpx;\n                            border: 2rpx dashed #2e80fe;\n                            padding-top: 28rpx;\n\n                            .up {\n                                margin: 0 auto;\n                                width: 210rpx;\n                                height: 130rpx;\n                            }\n                        }\n                    }\n\n                    .bottom {\n                        height: 66rpx;\n                        width: 100%;\n                        background-color: #2e80fe;\n                        font-size: 28rpx;\n                        font-weight: 400;\n                        color: #ffffff;\n                        text-align: center;\n                        line-height: 66rpx;\n                    }\n                }\n            }\n        }\n    }\n\n    .footer {\n        padding: 52rpx 30rpx;\n        width: 750rpx;\n        background: #ffffff;\n        box-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);\n        position: fixed;\n        bottom: 0;\n\n        .btn {\n            width: 690rpx;\n            height: 98rpx;\n            background: #2e80fe;\n            border-radius: 50rpx;\n            font-size: 32rpx;\n            font-weight: 500;\n            color: #ffffff;\n            line-height: 98rpx;\n            text-align: center;\n        }\n    }\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Settle.vue?vue&type=style&index=0&id=cde716de&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Settle.vue?vue&type=style&index=0&id=cde716de&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755159809898\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}