{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/promotion.vue?147f", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/promotion.vue?60b7", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/promotion.vue?c453", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/promotion.vue?0d7a", "uni-app:///user/promotion.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/promotion.vue?325f", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/promotion.vue?31ed"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userInfo", "extractTotalPrice", "yesterdayTotalCash", "totalCash", "userId", "isButtonDisabled", "onLoad", "methods", "tixian", "uni", "icon", "title", "url", "getInfo", "console", "goUrl", "getUserInfo"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAw1B,CAAgB,w2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC0E52B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MACA;QACAC;UACAC;UACAC;QACA;QACA;MACA;MACAF;QACAG;MACA;IACA;IACAC;MAAA;MACA;QACAC;QACA;QACA;QACA;MACA;IACA;IACAC;MACAN;QACAG;MACA;IACA;IACAI;MAAA;MACA;QACA;QACAF;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5HA;AAAA;AAAA;AAAA;AAA+lD,CAAgB,mjDAAG,EAAC,C;;;;;;;;;;;ACAnnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/promotion.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/promotion.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./promotion.vue?vue&type=template&id=3a7750ed&scoped=true&\"\nvar renderjs\nimport script from \"./promotion.vue?vue&type=script&lang=js&\"\nexport * from \"./promotion.vue?vue&type=script&lang=js&\"\nimport style0 from \"./promotion.vue?vue&type=style&index=0&id=3a7750ed&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3a7750ed\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/promotion.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion.vue?vue&type=template&id=3a7750ed&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<view class=\"header\">\n\t\t\t<!-- 标题 -->\n\t\t\t<view class=\"title-section\">\n\t\t\t\t邀好友 赚现金\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"\">\n\t\t\t\t<!-- 顶部图片区域 -->\n\t\t\t\t<view class=\"top-section\">\n\t\t\t\t\t<view class=\"top-image\">\n\t\t\t\t\t\t<image src=\"/user/static/cash_image.png\" mode=\"widthFix\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"invite-text\">\n\t\t\t\t\t\t邀请师傅和商家，接单下单都赚钱\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<!-- 底部现金图片区域 -->\n\t\t\t\t<view class=\"cash-section\">\n\t\t\t\t\t<view class=\"cash-image\">\n\t\t\t\t\t\t<image src=\"/user/static/top_image.png\" mode=\"widthFix\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"description-text\">\n\t\t\t\t\t\t每邀请一位师傅和商家入驻,师傅、商家订单验收完结后,邀请人每单可获得1%的奖励。\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"content-card\">\n\t\t    <view class=\"card-container\">\n\t\t        <view class=\"money-section\">\n\t\t            <view class=\"money-item\">\n\t\t                <view class=\"money-title\">当前佣金</view>\n\t\t                <view class=\"money-value\">￥{{totalCash?totalCash:'0.00'}}</view>\n\t\t            </view>\n\t\t            <view class=\"money-item\">\n\t\t                <view class=\"money-title\">今日收益</view>\n\t\t                <view class=\"money-value\">￥{{yesterdayTotalCash || '0.00'}}</view>\n\t\t            </view>\n\t\t            <view class=\"money-item\">\n\t\t                <view class=\"money-title\">累计已提</view>\n\t\t                <view class=\"money-value\">￥{{extractTotalPrice || '0'}}</view>\n\t\t            </view>\n\t\t            <view class=\"money-item withdraw-btn-container\">\n\t\t                <view class=\"withdraw-btn\" :class=\"{ 'btn-disabled': isButtonDisabled }\" @click=\"tixian\">立即提现</view>\n\t\t            </view>\n\t\t        </view>\n\n\t\t        <view class=\"function-section\">\n\t\t            <view class=\"function-item\" @click=\"goUrl('../user/business')\">\n\t\t\t\t\t\t<u-icon name=\"grid-fill\" color=\"#FF4D4F\" size=\"40\"></u-icon>\n\t\t                <text class=\"function-text\">邀请有礼</text>\n\t\t            </view>\n\t\t            <view class=\"function-item\" @click=\"goUrl('../user/statistics')\">\n\t\t\t\t\t\t<u-icon name=\"clock-fill\" color=\"#FF4D4F\" size=\"40\"></u-icon>\n\t\t                <text class=\"function-text\">邀请人统计</text>\n\t\t            </view>\n\t\t            <view class=\"function-item\" @click=\"goUrl('../user/Promotioncommission')\">\n\t\t\t\t\t\t<u-icon name=\"rmb-circle-fill\" color=\"#FF4D4F\" size=\"40\"></u-icon>\n\t\t                <text class=\"function-text\">邀请收益明细</text>\n\t\t            </view>\n\t\t            <view class=\"function-item\" @click=\"goUrl('../user/promotion_order')\">\n\t\t\t\t\t\t<u-icon name=\"file-text-fill\" color=\"#FF4D4F\" size=\"40\"></u-icon>\n\t\t                <text class=\"function-text\">邀请人订单</text>\n\t\t            </view>\n\t\t        </view>\n\t\t    </view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tuserInfo: {},\n\t\t\t\textractTotalPrice: '',\n\t\t\t\tyesterdayTotalCash: '',\n\t\t\t\ttotalCash: '',\n\t\t\t\tuserId: null, // 新增用户ID字段\n\t\t\t\tisButtonDisabled: false // 新增控制按钮禁用状态的字段\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.getInfo()\n\t\t\tthis.getUserInfo()\n\t\t},\n\t\tmethods: {\n\t\t\ttixian() {\n\t\t\t\tif (this.isButtonDisabled) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '提现功能暂不可用'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '../user/cashOut'\n\t\t\t\t})\n\t\t\t},\n\t\t\tgetInfo() {\n\t\t\t\tthis.$api.service.tgIndex().then(res => {\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tthis.extractTotalPrice = res.data.extractTotalPrice\n\t\t\t\t\tthis.yesterdayTotalCash = res.data.yesterdayTotalCash\n\t\t\t\t\tthis.totalCash = res.data.totalCash\n\t\t\t\t})\n\t\t\t},\n\t\t\tgoUrl(e) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: e\n\t\t\t\t})\n\t\t\t},\n\t\t\tgetUserInfo() {\n\t\t\t\tthis.$api.user.userInfo().then(res => {\n\t\t\t\t\tthis.userInfo = res.data\r\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tthis.userId = res.data.id || 11431 // 替换为实际获取userId的方式\n\t\t\t\t\tthis.isButtonDisabled = this.userId === 11431 // 判断是否为目标用户\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped lang=\"scss\">\n\t.page {\n\t\theight: 100vh;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\tbackground-image: url(\"data:image/jpeg;base64,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\");\r\n\t\t\t.header{\n\t\t\tpadding: 40rpx 30rpx 20rpx;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\t\t\tposition: relative;\n\t\t\tflex-shrink: 0;\n\t\t\t\n\t\t\t.title-section {\n\t\t\t\tfont-size: 60rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tcolor: #FFFFFF;\n\t\t\t\ttext-align: center;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t}\n\t\t\t\n\t\t\t.top-section {\n\t\t\t\tposition: relative;\n\t\t\t\twidth: 100%;\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\talign-items: center;\n\t\t\t\t-webkit-transform: translateZ(0);\n\t\t\t\ttransform: translateZ(0);\n\t\t\t\t\n\t\t\t\t.top-image {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t-webkit-transform: translateZ(0);\n\t\t\t\t\ttransform: translateZ(0);\n\t\t\t\t\t\n\t\t\t\t\timage {\n\t\t\t\t\t\tmax-width: 500rpx;\n\t\t\t\t\t\theight: auto;\n\t\t\t\t\t\t-webkit-transform: translateZ(0);\n\t\t\t\t\t\ttransform: translateZ(0);\n\t\t\t\t\t\t-webkit-backface-visibility: hidden;\n\t\t\t\t\t\tbackface-visibility: hidden;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.invite-text {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: 45%;\n\t\t\t\t\tleft: 50%;\n\t\t\t\t\t-webkit-transform: translate3d(-50%, -50%, 0);\n\t\t\t\t\ttransform: translate3d(-50%, -50%, 0);\n\t\t\t\t\tcolor: #FFF;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\tpadding: 15rpx 30rpx;\n\t\t\t\t\tborder-radius: 50rpx;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\tz-index: 2;\n\t\t\t\t\t-webkit-backface-visibility: hidden;\n\t\t\t\t\tbackface-visibility: hidden;\n\t\t\t\t\twill-change: transform;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.cash-section {\n\t\t\t\tposition: relative;\n\t\t\t\twidth: 100%;\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\talign-items: center;\n\t\t\t\t-webkit-transform: translateZ(0);\n\t\t\t\ttransform: translateZ(0);\n\t\t\t\t\n\t\t\t\t.cash-image {\n\t\t\t\t\twidth: 120%;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t-webkit-transform: translateZ(0);\n\t\t\t\t\ttransform: translateZ(0);\n\t\t\t\t\t\n\t\t\t\t\timage {\n\t\t\t\t\t\tmax-width: 500rpx;\n\t\t\t\t\t\theight: auto;\n\t\t\t\t\t\t-webkit-transform: translateZ(0);\n\t\t\t\t\t\ttransform: translateZ(0);\n\t\t\t\t\t\t-webkit-backface-visibility: hidden;\n\t\t\t\t\t\tbackface-visibility: hidden;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.description-text {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: 73%;\n\t\t\t\t\tleft: 50%;\n\t\t\t\t\t-webkit-transform: translate3d(-50%, -50%, 0);\n\t\t\t\t\ttransform: translate3d(-50%, -50%, 0);\n\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tline-height: 1.4;\n\t\t\t\t\tpadding: 15rpx;\n\t\t\t\t\tmax-width: 350rpx;\n\t\t\t\t\tz-index: 2;\n\t\t\t\t\t-webkit-backface-visibility: hidden;\n\t\t\t\t\tbackface-visibility: hidden;\n\t\t\t\t\twill-change: transform;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.content-card {\n\t\t\tflex: 1;\n\t\t\tpadding: 0 30rpx 30rpx;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\t\n\t\t\t.card-container {\n\t\t\t\tbackground: #FFFFFF;\n\t\t\t\tborder-radius: 30rpx;\n\t\t\t\tpadding: 30rpx;\n\t\t\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n\t\t\t\tflex: 1;\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\t\n\t\t\t\t.money-section {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-wrap: wrap;\n\t\t\t\t\tmargin-bottom: 30rpx;\n\t\t\t\t\t\n\t\t\t\t\t.money-item {\n\t\t\t\t\t\twidth: 50%;\n\t\t\t\t\t\tpadding-bottom: 30rpx;\n\t\t\t\t\t\t\n\t\t\t\t\t\t&:nth-child(1), &:nth-child(2) {\n\t\t\t\t\t\t\tborder-bottom: 2rpx solid #F5F5F5;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t&:nth-child(2), &:nth-child(4) {\n\t\t\t\t\t\t\tborder-left: 2rpx solid #F5F5F5;\n\t\t\t\t\t\t\tpadding-left: 25rpx;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t&:nth-child(3), &:nth-child(4) {\n\t\t\t\t\t\t\tpadding-top: 30rpx;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t.money-title {\n\t\t\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\t\t\tcolor: #888888;\n\t\t\t\t\t\t\tmargin-bottom: 12rpx;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t.money-value {\n\t\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.withdraw-btn-container {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tjustify-content: flex-start;\n\t\t\t\t\t\t\n\t\t\t\t\t\t.withdraw-btn {\n\t\t\t\t\t\t\tbackground: #FF4D4F;\n\t\t\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\t\t\tpadding: 15rpx 35rpx;\n\t\t\t\t\t\t\tborder-radius: 50rpx;\n\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t.btn-disabled {\n\t\t\t\t\t\t\tbackground: #cccccc !important;\n\t\t\t\t\t\t\tcolor: #666666 !important;\n\t\t\t\t\t\t\tpointer-events: none;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.function-section {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-wrap: wrap;\n\t\t\t\t\tflex: 1;\n\t\t\t\t\t\n\t\t\t\t\t.function-item {\n\t\t\t\t\t\twidth: 50%;\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\tflex-direction: column;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\tpadding: 20rpx 0;\n\t\t\t\t\t\t\n\t\t\t\t\t\t.function-icon {\n\t\t\t\t\t\t\twidth: 60rpx;\n\t\t\t\t\t\t\theight: 60rpx;\n\t\t\t\t\t\t\tmargin-bottom: 12rpx;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t.function-text {\n\t\t\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion.vue?vue&type=style&index=0&id=3a7750ed&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion.vue?vue&type=style&index=0&id=3a7750ed&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755159809924\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}