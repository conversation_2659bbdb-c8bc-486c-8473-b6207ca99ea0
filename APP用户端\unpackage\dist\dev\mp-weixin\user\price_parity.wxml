<view class="page data-v-027e28c9"><view class="header data-v-027e28c9"><image src="{{serviceInfo.cover}}" mode="scaleToFill" class="data-v-027e28c9"></image></view><view class="content data-v-027e28c9"><view class="card data-v-027e28c9"><view class="top data-v-027e28c9"><view class="title data-v-027e28c9">{{serviceInfo.title}}</view><block wx:if="{{serviceInfo.servicePriceType!=1&&type==0}}"><view class="price data-v-027e28c9">{{"￥"+serviceInfo.price}}</view></block></view><view class="bottom data-v-027e28c9"><view class="left data-v-027e28c9">已选：</view><view class="right data-v-027e28c9"><block wx:if="{{serviceInfo.servicePriceType==1}}"><view class="data-v-027e28c9">{{''+yikoujiaprice+''}}</view></block><block wx:for="{{chooseArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="tag data-v-027e28c9">{{item.name}}</view></block></view></view></view><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="chol data-v-027e28c9"><view class="choose data-v-027e28c9"><view class="title data-v-027e28c9"><block wx:if="{{item.isRequired==1}}"><label class="_span data-v-027e28c9">*</label></block>{{item.problemDesc}}</view><view class="desc data-v-027e28c9">{{item.problemContent}}</view><view class="cho_box data-v-027e28c9"><block wx:for="{{item.options}}" wx:for-item="newItem" wx:for-index="newIndex" wx:key="newIndex"><view data-event-opts="{{[['tap',[['chooseOne',[index,newIndex,'$0'],[[['list','id',item.id,'inputType']]]]]]]}}" class="box_item data-v-027e28c9" style="{{(newItem.choose?'border:2rpx solid #2E80FE;color: #2E80FE;':'')}}" bindtap="__e">{{''+newItem.name+''}}<view class="ok data-v-027e28c9" style="{{(newItem.choose?'':'display:none;')}}"><uni-icons vue-id="{{'4d47715d-1-'+index+'-'+newIndex}}" type="checkmarkempty" size="8" color="#fff" class="data-v-027e28c9" bind:__l="__l"></uni-icons></view></view></block></view></view><view class="fg data-v-027e28c9"></view></view></block><block wx:for="{{list2}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="chol data-v-027e28c9"><view class="choose data-v-027e28c9"><view class="title data-v-027e28c9"><block wx:if="{{item.isRequired==1}}"><label class="_span data-v-027e28c9">*</label></block>{{item.problemDesc}}</view><view class="desc data-v-027e28c9">{{item.problemContent}}</view><view class="input-container data-v-027e28c9" id="{{'input-container-'+index}}"><input class="form-input data-v-027e28c9" type="text" placeholder="{{'请输入'+item.problemDesc}}" cursor-spacing="10" confirm-type="done" adjust-position="{{false}}" auto-height="{{false}}" data-event-opts="{{[['focus',[['handleInputFocus',[index]]]],['blur',[['handleInputBlur',['$event']]]],['input',[['__set_model',['$0','val','$event',[]],['form.data.'+(index+$root.g1)+'']],['handleInput',['$event']]]]]}}" value="{{form.data[index+$root.g2].val}}" bindfocus="__e" bindblur="__e" bindinput="__e"/></view></view><view class="fg data-v-027e28c9"></view></view></block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="chol data-v-027e28c9"><view class="choose data-v-027e28c9"><view class="title data-v-027e28c9"><block wx:if="{{item.$orig.isRequired==1}}"><label class="_span data-v-027e28c9">*</label></block>{{item.$orig.problemDesc}}</view><view class="desc up data-v-027e28c9">{{item.$orig.problemContent}}</view><upload vue-id="{{'4d47715d-2-'+index}}" imagelist="{{form.data[item.g3].val}}" imgtype="{{item.g4}}" text="上传图片" imgsize="{{3}}" data-event-opts="{{[['^upload',[['imgUpload']]],['^del',[['imgUpload']]]]}}" bind:upload="__e" bind:del="__e" class="data-v-027e28c9" bind:__l="__l"></upload></view><view class="fg data-v-027e28c9"></view></view></block><view style="height:300rpx;" class="data-v-027e28c9"></view></view><block wx:if="{{showOrderModal}}"><view data-event-opts="{{[['tap',[['closeOrderModal',['$event']]]]]}}" class="cart-modal data-v-027e28c9" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="cart-modal-content data-v-027e28c9" catchtap="__e"><view class="modal-header data-v-027e28c9"><view class="modal-service-info data-v-027e28c9"><image class="modal-service-image data-v-027e28c9" src="{{serviceInfo.cover}}" mode="aspectFill"></image><view class="modal-service-details data-v-027e28c9"><view class="modal-service-title data-v-027e28c9">{{serviceInfo.title}}</view><block wx:if="{{serviceInfo.servicePriceType!=1&&type==0}}"><view class="modal-service-price data-v-027e28c9">{{'￥'+serviceInfo.price+''}}</view></block><view class="modal-quantity-section data-v-027e28c9"><view class="modal-quantity-title data-v-027e28c9">数量</view><view class="modal-quantity-control data-v-027e28c9"><view data-event-opts="{{[['tap',[['decreaseQuantity',['$event']]]]]}}" class="quantity-btn data-v-027e28c9" bindtap="__e">-</view><view class="quantity-input data-v-027e28c9">{{orderQuantity}}</view><view data-event-opts="{{[['tap',[['increaseQuantity',['$event']]]]]}}" class="quantity-btn data-v-027e28c9" bindtap="__e">+</view></view></view><view class="modal-urgent-section data-v-027e28c9"><view data-event-opts="{{[['tap',[['toggleUrgent',['$event']]]]]}}" class="modal-urgent-checkbox data-v-027e28c9" bindtap="__e"><view class="{{['checkbox-icon','data-v-027e28c9',(isUrgent)?'checked':'']}}"><block wx:if="{{isUrgent}}"><uni-icons vue-id="4d47715d-3" type="checkmarkempty" size="12" color="#fff" class="data-v-027e28c9" bind:__l="__l"></uni-icons></block></view><text class="checkbox-label data-v-027e28c9">是否加急</text></view></view></view></view><view data-event-opts="{{[['tap',[['closeOrderModal',['$event']]]]]}}" class="modal-close data-v-027e28c9" bindtap="__e"><uni-icons vue-id="4d47715d-4" type="clear" size="24" color="#999" class="data-v-027e28c9" bind:__l="__l"></uni-icons></view></view><scroll-view class="modal-scroll-content data-v-027e28c9" scroll-y="true"><view data-event-opts="{{[['tap',[['goToAddress',['$event']]]]]}}" class="modal-address-section data-v-027e28c9" bindtap="__e"><view class="modal-section-title data-v-027e28c9"><image class="section-icon data-v-027e28c9" src="../static/images/position.png" mode></image><text class="data-v-027e28c9">服务地址</text></view><view class="modal-address-content data-v-027e28c9"><view class="address-text data-v-027e28c9">{{mrAddress.address||'请选择服务地址'}}</view><block wx:if="{{mrAddress.address}}"><view class="address-detail data-v-027e28c9">{{mrAddress.userName+"\n\t\t\t\t\t\t\t"+mrAddress.mobile}}</view></block></view><uni-icons vue-id="4d47715d-5" name="arrow-right" size="16" color="#999" class="data-v-027e28c9" bind:__l="__l"></uni-icons></view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="modal-time-section data-v-027e28c9" bindtap="__e"><view class="modal-section-title data-v-027e28c9"><image class="section-icon data-v-027e28c9" src="../static/images/clock.png" mode></image><text class="data-v-027e28c9">预约时间</text></view><view class="modal-time-content data-v-027e28c9"><text class="data-v-027e28c9">{{conDate+(conTime?' '+conTime:'')}}</text></view><uni-icons vue-id="4d47715d-6" name="arrow-right" size="16" color="#999" class="data-v-027e28c9" bind:__l="__l"></uni-icons></view><view class="modal-notes-section data-v-027e28c9"><view class="modal-notes-title data-v-027e28c9">服务备注</view><textarea class="modal-notes-textarea data-v-027e28c9" placeholder="想要额外嘱咐工作人员的可以备注哦~" data-event-opts="{{[['input',[['__set_model',['','notes','$event',[]]]]]]}}" value="{{notes}}" bindinput="__e"></textarea></view></scroll-view><view class="modal-footer data-v-027e28c9"><view class="modal-footer-buttons data-v-027e28c9"><view data-event-opts="{{[['tap',[['confirmOrder',['$event']]]]]}}" class="{{['modal-order-btn','data-v-027e28c9',(isSubmittingOrder)?'submitting':'']}}" bindtap="__e">{{''+(isSubmittingOrder?'提交中...':'立即下单')+''}}</view></view></view></view></view></block><block wx:if="{{showTimeModal}}"><view data-event-opts="{{[['tap',[['closeTimeModal',['$event']]]]]}}" class="time-modal data-v-027e28c9" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="time-modal-content data-v-027e28c9" catchtap="__e"><view class="time-modal-header data-v-027e28c9"><view class="time-modal-title data-v-027e28c9">请选择时间</view><view data-event-opts="{{[['tap',[['closeTimeModal',['$event']]]]]}}" class="time-modal-close data-v-027e28c9" bindtap="__e"><uni-icons vue-id="4d47715d-7" type="clear" size="24" color="#999" class="data-v-027e28c9" bind:__l="__l"></uni-icons></view></view><view class="time-date-section data-v-027e28c9"><block wx:for="{{dateArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectDate',['$0',index],[[['dateArr','',index]]]]]]]}}" class="{{['time-date-item','data-v-027e28c9',(currentDate===index)?'active':'']}}" bindtap="__e"><view class="date-str data-v-027e28c9">{{item.str}}</view><view class="date-num data-v-027e28c9">{{item.date}}</view></view></block></view><scroll-view class="time-slots-section data-v-027e28c9" scroll-y="true"><view class="time-slots-grid data-v-027e28c9"><view class="time-slot-column data-v-027e28c9"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" data-event-params="{{({item,index})}}" class="{{['time-slot-item','data-v-027e28c9',(currentTime===index)?'active':'',(item.disabled)?'disabled':'']}}" bindtap="__e">{{''+item.time+''}}</view></block></view><view class="time-slot-column data-v-027e28c9"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" data-event-params="{{({item,index})}}" class="{{['time-slot-item','data-v-027e28c9',(currentTime===index+6)?'active':'',(item.disabled)?'disabled':'']}}" bindtap="__e">{{''+item.time+''}}</view></block></view></view></scroll-view><view class="time-modal-footer data-v-027e28c9"><view data-event-opts="{{[['tap',[['confirmTime',['$event']]]]]}}" class="time-confirm-btn data-v-027e28c9" bindtap="__e">确定预约时间</view></view></view></view></block><view class="footer data-v-027e28c9" style="{{(footerStyle)}}"><view data-event-opts="{{[['tap',[['confirmAddToCart',['$event']]]]]}}" class="{{['footer-item','footer-add-cart','data-v-027e28c9',(isAddingToCart)?'submitting':'']}}" bindtap="__e">{{''+(isAddingToCart?'加入中...':'加入购物车')+''}}</view><view data-event-opts="{{[['tap',[['handleOrderClick',['$event']]]]]}}" class="{{['footer-item','footer-order','data-v-027e28c9',(isSubmittingOrder)?'submitting':'']}}" bindtap="__e">{{''+(isSubmittingOrder?'提交中...':'立即下单')+''}}</view></view></view>