{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/skillsIndex.vue?d793", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/skillsIndex.vue?3585", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/skillsIndex.vue?a08b", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/skillsIndex.vue?7e71", "uni-app:///shifu/skillsIndex.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/skillsIndex.vue?c55b", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/skillsIndex.vue?6d64"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "categories", "selectedCategoryId", "loading", "error", "serviceIds", "shifuId", "userID", "scrollTop", "computed", "currentCategory", "methods", "selectCategory", "toggleSelect", "subCategory", "getSelectedCount", "isAllSelected", "toggleSelectAll", "item", "saveSettings", "serviceNames", "category", "serviceIdsString", "serviceNamesString", "uni", "console", "title", "icon", "duration", "success", "setTimeout", "delta", "getList", "$api", "response", "categoriesData", "Array", "getInfoS", "res", "scroll", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACqC;;;AAG/F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA01B,CAAgB,02BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACkF92B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QAAA;MAAA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACAC;;MAEA;MACA;QACA;UACA;QACA;MACA;QACA;UAAA;QAAA;MACA;IACA;IAEA;IACAC;MACA;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MACA;MAEA;MAEA;QACAC;;QAEA;QACA;UACA;YACA;UACA;QACA;UACA;YAAA;UAAA;QACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACAC;kBACA;oBACA;sBACAC;wBACA;0BACAD;wBACA;sBACA;oBACA;kBACA;;kBAEA;kBACAE;kBACAC;kBAEAC;kBACAA;kBAEAC;kBACAA;kBAEAD;oBACAE;oBACAC;oBACAC;oBACAC;sBACAC;wBACA;wBACAN;0BAAAO;wBAAA;sBACA;oBACA;kBACA;gBACA;kBACAP;oBACAE;oBACAC;kBACA;kBACAF;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAO;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBACAT;gBAEAU;gBAAA,KACAC;kBAAA;kBAAA;gBAAA;gBACAD;gBAAA;gBAAA;cAAA;gBAAA,MACAD;kBAAA;kBAAA;gBAAA;gBACAC;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAGA;gBACA;gBACAA;kBACA;oBACAd;sBACA;sBACA;wBACA;sBACA;oBACA;kBACA;gBACA;gBAEA;gBAEA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACAG;kBACAE;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAU;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAJ;cAAA;gBAAAK;gBACAb;gBAEA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAD;kBACAE;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAY;MACA;IACA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/QA;AAAA;AAAA;AAAA;AAAsuC,CAAgB,ytCAAG,EAAC,C;;;;;;;;;;;ACA1vC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/skillsIndex.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/skillsIndex.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./skillsIndex.vue?vue&type=template&id=6c7d5886&scoped=true&\"\nvar renderjs\nimport script from \"./skillsIndex.vue?vue&type=script&lang=js&\"\nexport * from \"./skillsIndex.vue?vue&type=script&lang=js&\"\nimport style0 from \"./skillsIndex.vue?vue&type=style&index=0&id=6c7d5886&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6c7d5886\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/skillsIndex.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./skillsIndex.vue?vue&type=template&id=6c7d5886&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.loading && !_vm.error ? _vm.categories.length : null\n  var g1 =\n    !_vm.loading && !_vm.error\n      ? _vm.currentCategory &&\n        _vm.currentCategory.children &&\n        _vm.currentCategory.children.length\n      : null\n  var g2 =\n    !_vm.loading && !_vm.error && g1\n      ? _vm.currentCategory.children.length\n      : null\n  var m0 = !_vm.loading && !_vm.error && g1 ? _vm.getSelectedCount() : null\n  var m1 = !_vm.loading && !_vm.error && g1 ? _vm.isAllSelected() : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./skillsIndex.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./skillsIndex.vue?vue&type=script&lang=js&\"", "\n<template>\n  <view class=\"page\">\n    <view class=\"main\">\n      <!-- 左侧父类分类列表 -->\n      <view class=\"left\">\n        <scroll-view scroll-y=\"true\" class=\"scrollL\">\n          <view v-if=\"loading\" class=\"loading\">\n            <text>加载中...</text>\n          </view>\n          <view v-else-if=\"error\" class=\"error\">\n            <text>{{ error }}</text>\n          </view>\n          <view v-else-if=\"!categories.length\" class=\"no-content\">\n            <text>暂无分类数据</text>\n          </view>\n          <view\n            v-else\n            class=\"left_item\"\n            v-for=\"category in categories\"\n            :key=\"category.id\"\n            @tap=\"selectCategory(category.id)\"\n            :class=\"{ active: selectedCategoryId === category.id }\"\n          >\n            <view class=\"category_name\">{{ category.name }}</view>\n          </view>\n        </scroll-view>\n      </view>\n\n      <!-- 右侧子类标签列表 -->\n      <view class=\"right\">\n        <scroll-view scroll-y=\"true\" class=\"scrollR\" :scroll-top=\"scrollTop\" @scroll=\"scroll\">\n          <view v-if=\"loading\" class=\"loading\">\n            <text>加载中...</text>\n          </view>\n          <view v-else-if=\"error\" class=\"error\">\n            <text>{{ error }}</text>\n          </view>\n          <view v-else-if=\"currentCategory && currentCategory.children && currentCategory.children.length\">\n            <!-- 分类标题和全选功能 -->\n            <view class=\"category_header\">\n              <view class=\"category_info\">\n                <view class=\"category_title\">{{ currentCategory.name }}</view>\n                <view class=\"category_stats\">\n                  <text class=\"total_count\">共{{ currentCategory.children.length }}项</text>\n                  <text class=\"selected_count\">已选{{ getSelectedCount() }}项</text>\n                </view>\n              </view>\n              <view class=\"select_all_btn\" @click=\"toggleSelectAll()\">\n                <text class=\"select_all_text\">{{ isAllSelected() ? '取消全选' : '全选' }}</text>\n              </view>\n            </view>\n\n            <!-- 子类标签列表 - 改为两列布局 -->\n            <view class=\"subcategory_grid\">\n              <view\n                class=\"subcategory_item\"\n                v-for=\"subCategory in currentCategory.children\"\n                :key=\"subCategory.id\"\n                @click=\"toggleSelect(subCategory)\"\n                :class=\"{ active: subCategory.selected }\"\n              >\n                <text class=\"subcategory_name\">{{ subCategory.name }}</text>\n                <view v-if=\"subCategory.selected\" class=\"selected_icon\">\n                  <text>✓</text>\n                </view>\n              </view>\n            </view>\n          </view>\n          <view v-else class=\"no-content\">\n            <text>暂无子分类</text>\n          </view>\n        </scroll-view>\n      </view>\n    </view>\n    <view class=\"footer\">\n      <button class=\"save_btn\" @click=\"saveSettings\">保存设置</button>\n    </view>\n  </view>\n</template>\n\n<script>\nimport $api from \"@/api/index.js\";\n\nexport default {\n  data() {\n    return {\n      categories: [],\n      selectedCategoryId: null,\n      loading: false,\n      error: null,\n      serviceIds: [], // 存储选中的子类ID数组\n      shifuId: '',\n      userID: '',\n      scrollTop: 0\n    };\n  },\n  computed: {\n    currentCategory() {\n      return this.categories.find(cat => cat.id === this.selectedCategoryId) || null;\n    }\n  },\n  methods: {\n    // 选择父类分类\n    selectCategory(id) {\n      this.selectedCategoryId = id;\n      this.scrollTop = 0; // 切换分类时重置滚动位置\n    },\n\n    // 切换子类选中状态\n    toggleSelect(subCategory) {\n      subCategory.selected = !subCategory.selected;\n\n      // 更新serviceIds数组\n      if (subCategory.selected) {\n        if (!this.serviceIds.includes(subCategory.id)) {\n          this.serviceIds.push(subCategory.id);\n        }\n      } else {\n        this.serviceIds = this.serviceIds.filter(id => id !== subCategory.id);\n      }\n    },\n\n    // 获取当前分类下已选中的数量\n    getSelectedCount() {\n      if (!this.currentCategory || !this.currentCategory.children) return 0;\n      return this.currentCategory.children.filter(item => item.selected).length;\n    },\n\n    // 检查是否全部选中\n    isAllSelected() {\n      if (!this.currentCategory || !this.currentCategory.children) return false;\n      return this.currentCategory.children.every(item => item.selected);\n    },\n\n    // 全选/取消全选\n    toggleSelectAll() {\n      if (!this.currentCategory || !this.currentCategory.children) return;\n\n      const allSelected = this.isAllSelected();\n\n      this.currentCategory.children.forEach(item => {\n        item.selected = !allSelected;\n\n        // 更新serviceIds数组\n        if (!allSelected) {\n          if (!this.serviceIds.includes(item.id)) {\n            this.serviceIds.push(item.id);\n          }\n        } else {\n          this.serviceIds = this.serviceIds.filter(id => id !== item.id);\n        }\n      });\n    },\n    // 保存设置\n    async saveSettings() {\n      try {\n        // 收集选中的服务名称\n        const serviceNames = [];\n        this.categories.forEach(category => {\n          if (category.children && category.children.length) {\n            category.children.forEach(subCategory => {\n              if (subCategory.selected) {\n                serviceNames.push(subCategory.name);\n              }\n            });\n          }\n        });\n\n        // 保存选中的服务ID和名称到本地存储\n        const serviceIdsString = this.serviceIds.join(\",\");\n        const serviceNamesString = serviceNames.join(\",\");\n\n        uni.setStorageSync(\"selectedServices\", serviceIdsString);\n        uni.setStorageSync(\"selectedServiceNames\", serviceNamesString);\n\n        console.log(\"Saved selectedServices:\", serviceIdsString);\n        console.log(\"Saved selectedServiceNames:\", serviceNamesString);\n\n        uni.showToast({\n          title: \"保存成功\",\n          icon: \"success\",\n          duration: 1500,\n          success: () => {\n            setTimeout(() => {\n              // 返回到 master_Info.vue 页面\n              uni.navigateBack({ delta: 1 });\n            }, 1500);\n          },\n        });\n      } catch (e) {\n        uni.showToast({\n          title: \"保存失败\",\n          icon: \"none\",\n        });\n        console.error(\"保存失败:\", e);\n      }\n    },\n    // 获取分类列表\n    async getList() {\n      this.loading = true;\n      this.error = null;\n      try {\n        const response = await $api.shifu.getNewSkill();\n        console.log(\"API Response:\", response);\n\n        let categoriesData = [];\n        if (Array.isArray(response)) {\n          categoriesData = response;\n        } else if (response.data && Array.isArray(response.data)) {\n          categoriesData = response.data;\n        } else {\n          throw new Error(\"无效或空的数据\");\n        }\n\n        // 初始化数据，包括selected状态和serviceIds\n        this.serviceIds = [];\n        categoriesData.forEach(category => {\n          if (category.children) {\n            category.children.forEach(subCategory => {\n              // 如果API返回的subCategory.selected为true，则添加到serviceIds\n              if (subCategory.selected) {\n                this.serviceIds.push(subCategory.id);\n              }\n            });\n          }\n        });\n\n        this.categories = categoriesData;\n\n        if (this.categories.length > 0) {\n          this.selectedCategoryId = this.categories[0].id;\n        }\n      } catch (err) {\n        this.error = \"数据加载失败: \" + err.message;\n        uni.showToast({\n          title: this.error,\n          icon: \"none\"\n        });\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 获取师傅信息\n    async getInfoS() {\n      try {\n        const res = await $api.shifu.getSInfo();\n        console.log(\"getSInfo Response:\", res);\n\n        this.shifuId = res.data.id;\n        this.userID = res.data.userId;\n\n        // 获取分类列表\n        await this.getList();\n      } catch (err) {\n        console.error(\"Error in getInfoS:\", err);\n        uni.showToast({\n          title: \"加载服务信息失败\",\n          icon: \"none\"\n        });\n      }\n    },\n\n    // 滚动事件\n    scroll(e) {\n      this.scrollTop = e.detail.scrollTop;\n    }\n  },\n  async onLoad() {\n    await this.getInfoS();\n  }\n};\n</script>\n\n<style scoped>\n.page {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background: #f8f9fa;\n}\n\n.main {\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n}\n\n.left {\n  width: 200rpx;\n  background: #f8f9fa;\n  border-right: 1rpx solid #dee2e6;\n}\n\n.scrollL {\n  height: 100%;\n}\n\n.left_item {\n  padding: 30rpx 20rpx;\n  font-size: 28rpx;\n  text-align: center;\n  border-left: 4rpx solid transparent;\n}\n\n.left_item.active {\n  color: #2e80fe;\n  background: #e6f0ff;\n  border-left-color: #2e80fe;\n}\n\n.right {\n  flex: 1;\n  background-color: #fff;\n  display: flex;\n  flex-direction: column;\n}\n\n.scrollR {\n  flex: 1;\n  height: 100%;\n}\n\n.category_header {\n  padding: 20rpx;\n  background: #2e80fe;\n  color: white;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.category_title {\n  font-size: 32rpx;\n  font-weight: bold;\n}\n\n.category_stats {\n  font-size: 24rpx;\n  opacity: 0.9;\n}\n\n.select_all_btn {\n  padding: 10rpx 20rpx;\n  background: rgba(255,255,255,0.2);\n  border-radius: 30rpx;\n}\n\n/* 改为网格布局 */\n.subcategory_grid {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 10rpx;\n}\n\n.subcategory_item {\n  width: calc(50% - 20rpx);\n  margin: 10rpx;\n  padding: 25rpx 15rpx;\n  background: #f8f9fa;\n  border-radius: 10rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  box-sizing: border-box;\n}\n\n.subcategory_item.active {\n  background: #e6f0ff;\n  color: #2e80fe;\n  border: 1rpx solid #2e80fe;\n}\n\n.subcategory_name {\n  font-size: 28rpx;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 80%;\n}\n\n.selected_icon {\n  color: #2e80fe;\n  font-weight: bold;\n}\n\n.footer {\n  padding: 20rpx;\n  background: #fff;\n  border-top: 1rpx solid #eee;\n}\n\n.save_btn {\n  background: #2e80fe;\n  color: white;\n  border-radius: 50rpx;\n}\n\n.loading, .error, .no-content {\n  padding: 30rpx;\n  text-align: center;\n  color: #999;\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./skillsIndex.vue?vue&type=style&index=0&id=6c7d5886&scoped=true&lang=css&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./skillsIndex.vue?vue&type=style&index=0&id=6c7d5886&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755159792731\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}