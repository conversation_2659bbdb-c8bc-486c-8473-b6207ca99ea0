<view class="debug-page data-v-0d67308b"><view class="navbar data-v-0d67308b"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="nav-left data-v-0d67308b" bindtap="__e"><text class="nav-icon data-v-0d67308b">‹</text></view><view class="nav-title data-v-0d67308b">协议调试</view><view class="nav-right data-v-0d67308b"></view></view><view class="test-section data-v-0d67308b"><view class="test-title data-v-0d67308b">API测试</view><button class="test-btn data-v-0d67308b" disabled="{{isLoading}}" data-event-opts="{{[['tap',[['testGetConfig',['$event']]]]]}}" bindtap="__e">测试getConfig接口</button><button class="test-btn data-v-0d67308b" disabled="{{isLoading}}" data-event-opts="{{[['tap',[['testGetLoginProtocol',['$event']]]]]}}" bindtap="__e">测试getLoginProtocol接口</button><button class="test-btn data-v-0d67308b" disabled="{{isLoading}}" data-event-opts="{{[['tap',[['testNavigateToService',['$event']]]]]}}" bindtap="__e">测试跳转服务协议</button><button class="test-btn data-v-0d67308b" disabled="{{isLoading}}" data-event-opts="{{[['tap',[['testNavigateToPrivacy',['$event']]]]]}}" bindtap="__e">测试跳转隐私政策</button><button data-event-opts="{{[['tap',[['clearLogs',['$event']]]]]}}" class="test-btn data-v-0d67308b" bindtap="__e">清空日志</button></view><block wx:if="{{apiResponse}}"><view class="response-section data-v-0d67308b"><view class="response-title data-v-0d67308b">API响应数据</view><view class="response-content data-v-0d67308b"><text class="response-text data-v-0d67308b">{{$root.g0}}</text></view></view></block><view class="log-section data-v-0d67308b"><view class="log-title data-v-0d67308b">调试日志</view><view class="log-content data-v-0d67308b"><block wx:for="{{logs}}" wx:for-item="log" wx:for-index="index" wx:key="index"><text class="{{['log-item','data-v-0d67308b',log.type]}}">{{''+log.message+''}}</text></block></view></view></view>