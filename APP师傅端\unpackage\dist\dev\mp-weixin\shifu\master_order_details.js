(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["shifu/master_order_details"],{

/***/ 746:
/*!************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/main.js?{"page":"shifu%2Fmaster_order_details"} ***!
  \************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/uni-stat/dist/uni-stat.es.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _master_order_details = _interopRequireDefault(__webpack_require__(/*! ./shifu/master_order_details.vue */ 747));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_master_order_details.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 747:
/*!*******************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_order_details.vue ***!
  \*******************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _master_order_details_vue_vue_type_template_id_53648d4e_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./master_order_details.vue?vue&type=template&id=53648d4e&scoped=true& */ 748);
/* harmony import */ var _master_order_details_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./master_order_details.vue?vue&type=script&lang=js& */ 750);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _master_order_details_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _master_order_details_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _master_order_details_vue_vue_type_style_index_0_id_53648d4e_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./master_order_details.vue?vue&type=style&index=0&id=53648d4e&scoped=true&lang=scss& */ 752);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 67);

var renderjs





/* normalize component */

var component = Object(_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _master_order_details_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _master_order_details_vue_vue_type_template_id_53648d4e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _master_order_details_vue_vue_type_template_id_53648d4e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "53648d4e",
  null,
  false,
  _master_order_details_vue_vue_type_template_id_53648d4e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "shifu/master_order_details.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 748:
/*!**************************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_order_details.vue?vue&type=template&id=53648d4e&scoped=true& ***!
  \**************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_order_details_vue_vue_type_template_id_53648d4e_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./master_order_details.vue?vue&type=template&id=53648d4e&scoped=true& */ 749);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_order_details_vue_vue_type_template_id_53648d4e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_order_details_vue_vue_type_template_id_53648d4e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_order_details_vue_vue_type_template_id_53648d4e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_order_details_vue_vue_type_template_id_53648d4e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 749:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_order_details.vue?vue&type=template&id=53648d4e&scoped=true& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uPopup: function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u-popup/u-popup */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u-popup/u-popup")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u-popup/u-popup.vue */ 1048))
    },
    "u-Input": function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u--input/u--input */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u--input/u--input")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u--input/u--input.vue */ 945))
    },
    uModal: function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u-modal/u-modal */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u-modal/u-modal")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u-modal/u-modal.vue */ 874))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var l2 = _vm.ready
    ? _vm.__map(_vm.Info.settingOrders, function (orderSetting, orderIndex) {
        var $orig = _vm.__get_orig(orderSetting)
        var l1 = _vm.__map(
          orderSetting.settingOrderList,
          function (item, itemIndex) {
            var $orig = _vm.__get_orig(item)
            var m0 = _vm.isValidImageUrl(item.val)
            var l0 = m0
              ? _vm.__map(
                  _vm.splitImageUrls(item.val),
                  function (url, imgIndex) {
                    var $orig = _vm.__get_orig(url)
                    var g0 = url.trim()
                    return {
                      $orig: $orig,
                      g0: g0,
                    }
                  }
                )
              : null
            var g1 = !m0 ? item.val && item.val.trim() !== "" : null
            return {
              $orig: $orig,
              m0: m0,
              l0: l0,
              g1: g1,
            }
          }
        )
        return {
          $orig: $orig,
          l1: l1,
        }
      })
    : null
  if (!_vm._isMounted) {
    _vm.e0 = function ($event, url, orderIndex, itemIndex, imgIndex) {
      var _temp = arguments[arguments.length - 1].currentTarget.dataset,
        _temp2 = _temp.eventParams || _temp["event-params"],
        url = _temp2.url,
        orderIndex = _temp2.orderIndex,
        itemIndex = _temp2.itemIndex,
        imgIndex = _temp2.imgIndex
      var _temp, _temp2
      return _vm.onImageError(url, orderIndex, itemIndex, imgIndex)
    }
    _vm.e1 = function ($event, url, orderSetting) {
      var _temp3 = arguments[arguments.length - 1].currentTarget.dataset,
        _temp4 = _temp3.eventParams || _temp3["event-params"],
        url = _temp4.url,
        orderSetting = _temp4.orderSetting
      var _temp3, _temp4
      return _vm.previewImage(url, orderSetting.settingOrderList)
    }
    _vm.e2 = function ($event) {
      _vm.isAcceptingOrder ? _vm.confirmAccept() : _vm.confirmBao()
    }
    _vm.e3 = function ($event) {
      _vm.confirmshow = false
    }
    _vm.e4 = function ($event) {
      _vm.masterModalShow = false
    }
    _vm.e5 = function ($event) {
      _vm.showNameIdModal = false
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        l2: l2,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 750:
/*!********************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_order_details.vue?vue&type=script&lang=js& ***!
  \********************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_order_details_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./master_order_details.vue?vue&type=script&lang=js& */ 751);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_order_details_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_order_details_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_order_details_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_order_details_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_order_details_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 751:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_order_details.vue?vue&type=script&lang=js& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 36));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 38));
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var Upload = function Upload() {
  Promise.all(/*! require.ensure | components/upload */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/upload")]).then((function () {
    return resolve(__webpack_require__(/*! @/components/upload.vue */ 842));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
// Adjust path to your upload.vue
var _default = {
  components: {
    Upload: Upload
  },
  data: function data() {
    return {
      ready: false,
      id: '',
      configInfo: '',
      selectedItem: {},
      payType: {},
      Info: {},
      goodID: '',
      typeSwitch: 0,
      // 0的订单立即接单  1是立即报价
      imageErrors: [],
      // Track images that failed to load
      fallbackImage: '../static/images/placeholder.png',
      // Placeholder image path
      showImageModal: false,
      // Control image modal visibility
      currentImage: '',
      // Store the currently displayed image URL
      // 报价/接单相关数据
      show: false,
      getconfigs: '',
      confirmshow: false,
      masterModalShow: false,
      showNameIdModal: false,
      // New state for the name/ID modal
      content: '确认接下该订单吗',
      input: '',
      orderData: '',
      tmplIds: [' vR1qJM-SEYbGnvXdl4HQ5D2Nf7USnBgcmeov8slExOo', 'DxiqXzK4yxCTYAqmeK9lEs0A5-XCF9Fy7kSyX2vmnk', 'HVNlAWjUm-wjtFxYizNdqzPvrYvofmysaXs_iZ0T1Gs'],
      tempForm: {
        // Temporary form for name/ID input
        coachName: '',
        idCode: '',
        id_card1: [],
        id_card2: []
      },
      isAcceptingOrder: false // New state to distinguish between quote and accept order
    };
  },

  computed: {
    orderDetails: function orderDetails() {
      return [{
        label: '订单单号',
        value: this.formatOrderCode(this.selectedItem.orderCode),
        alignRight: true
      }, {
        label: '服务内容',
        value: this.selectedItem.goodsName || ''
      }, {
        label: '下单时间',
        value: this.selectedItem.createTime || ''
      }, {
        label: '联系方式',
        value: this.formatMobile(this.selectedItem.mobile)
      }, {
        label: '服务定位',
        value: this.formatAddress(this.selectedItem.addressInfo),
        alignRight: true
      }, {
        label: '服务地址',
        value: this.formatAddress(this.selectedItem.address),
        alignRight: true
      }, {
        label: '门牌号',
        value: this.formatHouseNumber(this.selectedItem.houseNumber),
        alignRight: true
      }];
    },
    // 判断是否显示报价按钮（根据订单类型）
    shouldShowQuoteButton: function shouldShowQuoteButton() {
      return this.selectedItem.type === 1; // type为1时显示报价按钮
    }
  },

  methods: {
    // Input validation method
    validateInput: function validateInput(e) {
      var value = e.detail ? e.detail.value : e;

      // Remove all Chinese characters
      value = value.replace(/[\u4e00-\u9fa5]/g, '');

      // Only allow numbers and decimal point
      value = value.replace(/[^\d.]/g, '');

      // Handle decimal point logic
      var parts = value.split('.');
      if (parts.length > 2) {
        // If there are multiple decimal points, keep only the first
        value = parts[0] + '.' + parts.slice(1).join('');
      }
      if (parts.length === 2) {
        // If there is a decimal part, limit to two digits after the decimal point
        if (parts[1].length > 2) {
          parts[1] = parts[1].substring(0, 2);
          value = parts[0] + '.' + parts[1];
        }
        // If the first character after the decimal is not a digit, remove it
        if (value.length > 0 && value.charAt(value.length - 1) === '.' && isNaN(parseInt(value.charAt(value.length - 2)))) {
          value = value.substring(0, value.length - 1);
        }
      }

      // Prevent multiple zeros at the beginning (except for 0. scenarios)
      if (value.length > 1 && value.charAt(0) === '0' && value.charAt(1) !== '.') {
        value = value.substring(1);
      }

      // Update input value
      this.input = value;
    },
    // Handle quote button click
    handleQuote: function handleQuote() {
      this.isAcceptingOrder = false; // Set to false for quoting
      this.textsss();
      this.orderData = this.selectedItem;
      this.show = true;
    },
    // Handle accept order button click
    handleAcceptOrder: function handleAcceptOrder() {
      this.isAcceptingOrder = true; // Set to true for accepting order
      this.textsss(); // Still good to subscribe messages
      this.orderData = this.selectedItem;
      this.show = true; // Show the same popup
    },
    // Subscribe message handling
    textsss: function textsss() {
      var infodata = uni.getStorageSync('shiInfo') ? JSON.parse(uni.getStorageSync('shiInfo')) : {};
      if (infodata.status === 2) {
        uni.requestSubscribeMessage({
          tmplIds: this.tmplIds,
          success: function success(res) {
            console.log('requestSubscribeMessage result:', res);
          },
          fail: function fail(err) {
            console.log('requestSubscribeMessage failed:', err);
          }
        });
      }
    },
    close: function close() {
      this.show = false;
      this.input = '';
      this.isAcceptingOrder = false; // Reset the flag when closing the popup
    },
    // Confirm quote
    confirmBao: function confirmBao() {
      var _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var updatedOrderData, res;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                if (!(_this.input == '' || _this.input == 0)) {
                  _context.next = 3;
                  break;
                }
                uni.showToast({
                  icon: 'none',
                  title: '请输入报价（不能为0哦）'
                });
                return _context.abrupt("return");
              case 3:
                updatedOrderData = {
                  orderId: _this.id,
                  price: _this.input,
                  goodsId: _this.goodID
                };
                _context.prev = 4;
                _context.next = 7;
                return _this.$api.shifu.updateBao(updatedOrderData);
              case 7:
                res = _context.sent;
                console.log(res);
                if (res.data === -1) {
                  _this.masterModalShow = true;
                }
                if (!(res.data === 1 || res.data === 4)) {
                  _context.next = 13;
                  break;
                }
                uni.showToast({
                  icon: 'none',
                  title: res.msg
                });
                return _context.abrupt("return");
              case 13:
                if (!(res.data === -2)) {
                  _context.next = 17;
                  break;
                }
                uni.showToast({
                  icon: 'none',
                  title: res.msg
                });
                _this.showNameIdModal = true; // Show the new modal
                return _context.abrupt("return");
              case 17:
                if (!(res.code === "-1")) {
                  _context.next = 22;
                  break;
                }
                uni.showToast({
                  icon: 'none',
                  title: res.msg
                });
                return _context.abrupt("return");
              case 22:
                uni.showToast({
                  icon: 'success',
                  title: '报价成功'
                });
                _this.close();
                setTimeout(function () {
                  uni.navigateTo({
                    url: "/shifu/master_bao_list?id=1&goodId=".concat(_this.goodID, "&fromQuote=true")
                  });
                }, 1000);
              case 25:
                _context.next = 31;
                break;
              case 27:
                _context.prev = 27;
                _context.t0 = _context["catch"](4);
                uni.showToast({
                  icon: 'fail',
                  title: _context.t0.message || '报价失败'
                });
                _this.close();
              case 31:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[4, 27]]);
      }))();
    },
    // Confirm accept order
    confirmAccept: function confirmAccept() {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var acceptOrderData, res;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                acceptOrderData = {
                  orderId: _this2.id,
                  goodsId: _this2.goodID
                };
                console.log(acceptOrderData);
                _context2.prev = 2;
                _context2.next = 5;
                return _this2.$api.shifu.oneAcceptOrders(acceptOrderData);
              case 5:
                res = _context2.sent;
                // Assuming a method like `acceptOrder` exists
                console.log(res);
                if (res.data === -1) {
                  _this2.masterModalShow = true;
                }
                if (!(res.data === 1 || res.data === 4)) {
                  _context2.next = 11;
                  break;
                }
                uni.showToast({
                  icon: 'none',
                  title: res.msg
                });
                return _context2.abrupt("return");
              case 11:
                if (!(res.data === -2)) {
                  _context2.next = 15;
                  break;
                }
                uni.showToast({
                  icon: 'none',
                  title: res.msg
                });
                _this2.showNameIdModal = true; // Show the new modal
                return _context2.abrupt("return");
              case 15:
                if (!(res.code === "-1")) {
                  _context2.next = 20;
                  break;
                }
                uni.showToast({
                  icon: 'none',
                  title: res.msg
                });
                return _context2.abrupt("return");
              case 20:
                uni.showToast({
                  icon: 'success',
                  title: '接单成功'
                });
                _this2.close();
                setTimeout(function () {
                  uni.navigateTo({
                    // Navigate to the appropriate page after accepting the order
                    url: "/shifu/master_my_order" // Example: Navigate to master's current orders
                  });
                }, 1000);
              case 23:
                _context2.next = 29;
                break;
              case 25:
                _context2.prev = 25;
                _context2.t0 = _context2["catch"](2);
                uni.showToast({
                  icon: 'fail',
                  title: _context2.t0.message || '接单失败'
                });
                _this2.close();
              case 29:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[2, 25]]);
      }))();
    },
    // New method for handling image uploads in the tempForm
    imgUploadTemp: function imgUploadTemp(e) {
      console.log('imgUploadTemp event:', e);
      var imagelist = e.imagelist,
        imgtype = e.imgtype;
      this.$set(this.tempForm, imgtype, imagelist);
    },
    // New method to save name and ID info
    saveNameIdInfo: function saveNameIdInfo() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var _this3$tempForm, coachName, idCode, id_card1, id_card2, namePattern, idPattern, shifuid, userId, payload, res;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _this3$tempForm = _this3.tempForm, coachName = _this3$tempForm.coachName, idCode = _this3$tempForm.idCode, id_card1 = _this3$tempForm.id_card1, id_card2 = _this3$tempForm.id_card2; // 校验必填项
                if (!(!coachName || !idCode || id_card1.length === 0 || id_card2.length === 0)) {
                  _context3.next = 4;
                  break;
                }
                uni.showToast({
                  icon: 'none',
                  title: '请填写所有必填项并上传照片'
                });
                return _context3.abrupt("return");
              case 4:
                // 校验姓名格式：2-20个汉字，可包含·或•
                namePattern = /^[\u4e00-\u9fa5·•]{2,20}$/;
                if (namePattern.test(coachName)) {
                  _context3.next = 8;
                  break;
                }
                uni.showToast({
                  icon: 'none',
                  title: '姓名必须为2-20个汉字，可包含·或•'
                });
                return _context3.abrupt("return");
              case 8:
                // 校验身份证号格式：18位有效格式
                idPattern = /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/;
                if (idPattern.test(idCode)) {
                  _context3.next = 12;
                  break;
                }
                uni.showToast({
                  icon: 'none',
                  title: '身份证号必须是18位有效格式'
                });
                return _context3.abrupt("return");
              case 12:
                shifuid = JSON.parse(uni.getStorageSync('shiInfo'));
                userId = uni.getStorageSync('userId');
                console.log(shifuid);
                console.log(userId);
                // Construct the payload for saving name and ID card information
                payload = {
                  coachName: coachName,
                  idCode: idCode,
                  id: shifuid.shifuId,
                  // Corrected payload to join the image paths into a single comma-separated string
                  idCard: [id_card1[0].path, id_card2[0].path].join(',')
                };
                _context3.prev = 17;
                _context3.next = 20;
                return _this3.$api.shifu.postCrad(payload);
              case 20:
                res = _context3.sent;
                // Replace with your actual API call
                console.log(res);
                if (res.code === "200") {
                  // Assuming "200" means success
                  uni.showToast({
                    icon: 'success',
                    title: '身份信息保存成功'
                  });
                  _this3.showNameIdModal = false;
                  // You might want to re-attempt the quote submission or refresh data here
                  // For now, let's just close the modal.
                } else {
                  uni.showToast({
                    icon: 'none',
                    title: res.msg || '身份信息保存失败'
                  });
                }
                _context3.next = 28;
                break;
              case 25:
                _context3.prev = 25;
                _context3.t0 = _context3["catch"](17);
                uni.showToast({
                  icon: 'error',
                  title: _context3.t0.message || '身份信息保存失败'
                });
              case 28:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[17, 25]]);
      }))();
    },
    // Jump to master settlement page
    goToSettle: function goToSettle() {
      this.masterModalShow = false;
      uni.redirectTo({
        url: '/shifu/Settle'
      });
    },
    // Confirm modal handler
    confirmRe: function confirmRe() {
      this.confirmshow = false;
      // Add any additional logic here if needed
    },
    // Format mobile number with ***** in the middle, unless payType.payType is 7, 3, or 5
    formatMobile: function formatMobile(mobile) {
      if (!mobile) return '';
      // You commented out the condition below. If payType.payType is NOT 7, 3, or 5, you want to mask.
      // If you want to show the full number when payType.payType is 7, 3, or 5, uncomment the line below:
      // if (this.payType.payType === 7 || this.payType.payType === 3 || this.payType.payType === 5) return mobile;

      // Otherwise, mask the number
      // return mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
      return mobile; // Currently, no masking is applied based on your original commented code
    },
    // Format address to hide last 7 characters, unless payType.payType is 7, 3, or 5
    formatAddress: function formatAddress(address) {
      if (!address) return '';
      // You commented out the condition below. If payType.payType is NOT 7, 3, or 5, you want to mask.
      // If you want to show the full address when payType.payType is 7, 3, or 5, uncomment the line below:
      // if (this.payType.payType === 7 || this.payType.payType === 3 || this.payType.payType === 5) return address;

      // Otherwise, mask the address
      // return address.length > 7 ? address.substring(0, address.length - 7) + '*******' : address;
      return address; // Currently, no masking is applied based on your original commented code
    },
    // Format house number with ***** in the middle, unless payType.payType is 7, 3, or 5
    formatHouseNumber: function formatHouseNumber(houseNumber) {
      if (!houseNumber) return '';
      if (this.payType.payType === 7) return houseNumber;
      return houseNumber; // No masking applied based on your original logic
    },
    // Format orderCode (keeping original format but removing '无')
    formatOrderCode: function formatOrderCode(orderCode) {
      return orderCode || '';
    },
    // Validate if the URL(s) contain valid image URLs
    isValidImageUrl: function isValidImageUrl(val) {
      if (!val || typeof val !== 'string') return false;
      var urls = val.split(',').map(function (url) {
        return url.trim();
      });
      var imageRegex = /^(https?:\/\/).*\.(png|jpg|jpeg|gif|bmp|webp)$/i;
      return urls.some(function (url) {
        return imageRegex.test(url);
      });
    },
    // Split comma-separated image URLs
    splitImageUrls: function splitImageUrls(val) {
      if (!val || typeof val !== 'string') return [];
      return val.split(',').map(function (url) {
        return url.trim();
      }).filter(function (url) {
        return url !== '';
      });
    },
    // Handle navigation
    godh: function godh() {
      uni.openLocation({
        latitude: Number(this.selectedItem.lat) || 0,
        longitude: Number(this.selectedItem.lng) || 0,
        scale: 18,
        name: this.selectedItem.address || '未知地址',
        address: this.selectedItem.addressInfo || '未知地址信息',
        success: function success() {
          return console.log('Navigation opened');
        },
        fail: function fail(err) {
          return console.error('Navigation error:', err);
        }
      });
    },
    // Fetch order details
    getDetail: function getDetail() {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var res;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                console.log(_this4.typeSwitch);
                _context4.prev = 1;
                _context4.next = 4;
                return _this4.$api.shifu.orderdetM(_this4.id);
              case 4:
                res = _context4.sent;
                console.log('API Response:', res);
                _this4.goodID = res.data.goodsId ? res.data.goodsId : _this4.goodID;
                _this4.typeSwitch = res.data.type ? res.data.type : _this4.typeSwitch;
                // console.log('coachStatus:',this.typeSwitch);
                // if (res.data.coachStatus === 1) {
                // 	uni.showToast({
                // 		title: '师傅状态在审核中',
                // 		icon: 'none'
                // 	});
                // }
                // if (res.data.coachStatus === -1 || res.data.coachStatus === 4) {
                // 	uni.showToast({
                // 		title: '你还不是师傅',
                // 		icon: 'none'
                // 	});
                // }
                _this4.Info = res.data || {};
                _this4.selectedItem = {
                  orderCode: res.data.orderCode || '',
                  goodsName: res.data.goodsName || '',
                  createTime: res.data.createTime || 0,
                  mobile: res.data.mobile || '',
                  addressInfo: res.data.addressInfo || '',
                  address: res.data.address || '',
                  houseNumber: res.data.houseNumber || '',
                  lat: res.data.lat || 0,
                  lng: res.data.lng || 0,
                  type: res.data.type || 0,
                  // Add order type
                  id: res.data.id || _this4.id // Add order ID
                };
                _context4.next = 16;
                break;
              case 12:
                _context4.prev = 12;
                _context4.t0 = _context4["catch"](1);
                console.error('API Error:', _context4.t0);
                uni.showToast({
                  title: '获取订单详情失败',
                  icon: 'none'
                });
              case 16:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[1, 12]]);
      }))();
    },
    // Handle image load errors
    onImageError: function onImageError(url, orderIndex, itemIndex, imgIndex) {
      console.error("Failed to load image: ".concat(url));
      // No need to track imageErrors array in data, directly update the image source
      // this.imageErrors.push(`${orderIndex}-${itemIndex}-${imgIndex}`); // This might not be needed anymore

      // Find the specific item and update its image URL to the fallback
      var targetItem = this.Info.settingOrders[orderIndex].settingOrderList[itemIndex];
      var urls = this.splitImageUrls(targetItem.val);
      urls[imgIndex] = this.fallbackImage;
      // Ensure reactivity with Vue.set for nested arrays
      this.$set(this.Info.settingOrders[orderIndex].settingOrderList, itemIndex, _objectSpread(_objectSpread({}, targetItem), {}, {
        val: urls.join(',')
      }));
    },
    // Show image using uni.previewImage
    previewImage: function previewImage(url, settingOrderList) {
      var _this5 = this;
      var urlsToPreview = settingOrderList.flatMap(function (item) {
        return _this5.splitImageUrls(item.val);
      });
      uni.previewImage({
        urls: urlsToPreview.filter(function (u) {
          return _this5.isValidImageUrl(u);
        }),
        // Filter to ensure valid image URLs are passed
        current: url
      });
    },
    // Close image modal
    closeImageModal: function closeImageModal() {
      this.showImageModal = false;
      this.currentImage = '';
    }
  },
  onLoad: function onLoad(options) {
    var _this6 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
      return _regenerator.default.wrap(function _callee5$(_context5) {
        while (1) {
          switch (_context5.prev = _context5.next) {
            case 0:
              console.log(options);
              _this6.$api.base.getConfig().then(function (res) {
                // console.log(res)
                _this6.getconfigs = res;
                console.log(_this6.getconfigs.masterNotice);
              });
              // this.configInfo = uni.getStorageSync('configInfo')
              // console.log(this.configInfo)
              _this6.id = options.id || '';
              _this6.goodID = options.goodsId;
              _this6.typeSwitch = Number(options.type); // Ensure typeSwitch is a number for strict comparison
              console.log(_this6.goodID);
              console.log(_this6.typeSwitch);
              _this6.payType = uni.getStorageSync('orderdetails') || {};
              console.log('payType:', _this6.payType);
              console.log('payType.payType:', _this6.payType.payType);
              console.log('Page options:', options);
              if (_this6.id) {
                _context5.next = 15;
                break;
              }
              console.error('No order ID provided');
              uni.showToast({
                title: '订单ID缺失',
                icon: 'none'
              });
              return _context5.abrupt("return");
            case 15:
              _context5.next = 17;
              return _this6.getDetail();
            case 17:
              _this6.ready = true;
            case 18:
            case "end":
              return _context5.stop();
          }
        }
      }, _callee5);
    }))();
  },
  onUnload: function onUnload() {
    uni.removeStorageSync('orderdetails');
    console.log('Removed orderdetails from local storage');
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 752:
/*!*****************************************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_order_details.vue?vue&type=style&index=0&id=53648d4e&scoped=true&lang=scss& ***!
  \*****************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_order_details_vue_vue_type_style_index_0_id_53648d4e_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./master_order_details.vue?vue&type=style&index=0&id=53648d4e&scoped=true&lang=scss& */ 753);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_order_details_vue_vue_type_style_index_0_id_53648d4e_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_order_details_vue_vue_type_style_index_0_id_53648d4e_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_order_details_vue_vue_type_style_index_0_id_53648d4e_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_order_details_vue_vue_type_style_index_0_id_53648d4e_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_order_details_vue_vue_type_style_index_0_id_53648d4e_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 753:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_order_details.vue?vue&type=style&index=0&id=53648d4e&scoped=true&lang=scss& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[746,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../.sourcemap/mp-weixin/shifu/master_order_details.js.map