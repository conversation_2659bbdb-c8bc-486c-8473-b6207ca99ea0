<view class="page data-v-cde716de"><block wx:if="{{flag}}"><u-picker vue-id="eefac2d6-1" show="{{showCity}}" loading="{{loading}}" columns="{{columnsCity}}" keyName="title" data-ref="uPicker" data-event-opts="{{[['^change',[['changeH<PERSON>ler']]],['^cancel',[['e0']]],['^confirm',[['confirmCity']]]]}}" bind:change="__e" bind:cancel="__e" bind:confirm="__e" class="data-v-cde716de vue-ref" bind:__l="__l"></u-picker></block><u-modal vue-id="eefac2d6-2" show="{{showMoney}}" showCancelButton="{{true}}" cancelText="再想想" data-event-opts="{{[['^confirm',[['confirmMoney']]],['^cancel',[['e1']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-cde716de" bind:__l="__l" vue-slots="{{['default']}}"><view class="slot-content data-v-cde716de"><rich-text nodes="{{contentMoney}}" class="data-v-cde716de"></rich-text></view></u-modal><u-modal vue-id="eefac2d6-3" show="{{show}}" title="{{title}}" showCancelButton="{{true}}" confirmText="同意" cancelText="不同意" data-event-opts="{{[['^confirm',[['confirmModel']]],['^cancel',[['cancelModel']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-cde716de" bind:__l="__l" vue-slots="{{['default']}}"><view class="slot-content data-v-cde716de"><rich-text nodes="{{entryNotice}}" class="data-v-cde716de"></rich-text></view></u-modal><block wx:if="{{shInfo.status==4}}"><u-modal vue-id="eefac2d6-4" show="{{showSh}}" title="驳回原因" confirmText="确定" content="{{shInfo.sh_text}}" data-event-opts="{{[['^confirm',[['e2']]]]}}" bind:confirm="__e" class="data-v-cde716de" bind:__l="__l"></u-modal></block><block wx:if="{{shInfo.status}}"><view data-event-opts="{{[['tap',[['shDetail',['$event']]]]]}}" class="header data-v-cde716de" style="{{('color:'+arr[shInfo.status-1].color)}}" bindtap="__e">{{''+arr[shInfo.status-1].text+''}}</view></block><view class="main data-v-cde716de"><view class="main_item data-v-cde716de"><view class="title data-v-cde716de"><label class="_span data-v-cde716de">*</label>姓名</view><input type="text" placeholder="请输入姓名" data-event-opts="{{[['input',[['__set_model',['$0','coachName','$event',[]],['form']]]]]}}" value="{{form.coachName}}" bindinput="__e" class="data-v-cde716de"/></view><view class="main_item data-v-cde716de"><view class="title data-v-cde716de"><label class="_span data-v-cde716de">*</label>手机号</view><input type="text" placeholder="请输入手机号" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['form']]]]]}}" value="{{form.mobile}}" bindinput="__e" class="data-v-cde716de"/></view><view class="main_item data-v-cde716de"><view class="title data-v-cde716de"><label class="_span data-v-cde716de">*</label>性别</view><u-radio-group bind:input="__e" vue-id="eefac2d6-5" placement="row" value="{{form.sex}}" data-event-opts="{{[['^input',[['__set_model',['$0','sex','$event',[]],['form']]]]]}}" class="data-v-cde716de" bind:__l="__l" vue-slots="{{['default']}}"><u-radio vue-id="{{('eefac2d6-6')+','+('eefac2d6-5')}}" customStyle="{{({marginRight:'20px'})}}" label="男" name="{{0}}" class="data-v-cde716de" bind:__l="__l"></u-radio><u-radio vue-id="{{('eefac2d6-7')+','+('eefac2d6-5')}}" label="女" name="{{1}}" class="data-v-cde716de" bind:__l="__l"></u-radio></u-radio-group></view><view class="main_item data-v-cde716de"><view class="title data-v-cde716de"><label class="_span data-v-cde716de">*</label>从业年份</view><input type="text" placeholder="请输入从业年份" data-event-opts="{{[['input',[['__set_model',['$0','workTime','$event',[]],['form']]]]]}}" value="{{form.workTime}}" bindinput="__e" class="data-v-cde716de"/></view><view class="main_item data-v-cde716de"><view class="title data-v-cde716de"><label class="_span data-v-cde716de">*</label>选择区域</view><input type="text" placeholder="请选择代理区域" disabled="{{true}}" data-event-opts="{{[['tap',[['e3',['$event']]]],['input',[['__set_model',['$0','city','$event',[]],['form']]]]]}}" value="{{form.city}}" bindtap="__e" bindinput="__e" class="data-v-cde716de"/></view><view class="main_item data-v-cde716de"><view class="title data-v-cde716de"><label class="_span data-v-cde716de">*</label>详细地址</view><input type="text" placeholder="请输入详细地址" data-event-opts="{{[['input',[['__set_model',['$0','address','$event',[]],['form']]]]]}}" value="{{form.address}}" bindinput="__e" class="data-v-cde716de"/></view><view class="main_item data-v-cde716de"><view class="title data-v-cde716de"><label class="_span data-v-cde716de">*</label>自我介绍(非必填)</view><input type="text" placeholder="请输入自我介绍" data-event-opts="{{[['input',[['__set_model',['$0','text','$event',[]],['form']]]]]}}" value="{{form.text}}" bindinput="__e" class="data-v-cde716de"/></view><view class="main_item data-v-cde716de"><view class="title data-v-cde716de"><label class="_span data-v-cde716de">*</label>身份证号</view><input type="text" placeholder="请输入身份证号" data-event-opts="{{[['input',[['__set_model',['$0','idCode','$event',[]],['form']]]]]}}" value="{{form.idCode}}" bindinput="__e" class="data-v-cde716de"/></view><view class="main_item data-v-cde716de"><view class="title data-v-cde716de"><label class="_span data-v-cde716de">*</label>上传身份证照片</view><view class="card data-v-cde716de"><view class="card_item data-v-cde716de"><view class="top data-v-cde716de"><view class="das data-v-cde716de"><view class="up data-v-cde716de"><upload vue-id="eefac2d6-8" imagelist="{{form.id_card1}}" imgtype="id_card1" imgclass="id_card_box" text="身份证人像面" imgsize="{{1}}" data-event-opts="{{[['^upload',[['imgUpload']]],['^del',[['imgUpload']]]]}}" bind:upload="__e" bind:del="__e" class="data-v-cde716de" bind:__l="__l"></upload></view></view></view><view class="bottom data-v-cde716de">拍摄人像面</view></view><view class="card_item data-v-cde716de"><view class="top data-v-cde716de"><view class="das data-v-cde716de"><view class="up data-v-cde716de"><upload vue-id="eefac2d6-9" imagelist="{{form.id_card2}}" imgtype="id_card2" imgclass="id_card_box" text="身份证国徽面" imgsize="{{1}}" data-event-opts="{{[['^upload',[['imgUpload']]],['^del',[['imgUpload']]]]}}" bind:upload="__e" bind:del="__e" class="data-v-cde716de" bind:__l="__l"></upload></view></view></view><view class="bottom data-v-cde716de">拍摄国徽面</view></view></view></view><view class="main_item data-v-cde716de"><view class="title data-v-cde716de"><label class="_span data-v-cde716de">*</label>上传形象照片</view><upload vue-id="eefac2d6-10" imagelist="{{form.self_img}}" imgtype="self_img" imgclass text="形象照片" imgsize="{{3}}" data-event-opts="{{[['^upload',[['imgUpload']]],['^del',[['imgUpload']]]]}}" bind:upload="__e" bind:del="__e" class="data-v-cde716de" bind:__l="__l"></upload></view></view><block wx:if="{{!shInfo.status}}"><view class="footer data-v-cde716de"><view data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" class="btn data-v-cde716de" bindtap="__e">立即提交</view></view></block></view>