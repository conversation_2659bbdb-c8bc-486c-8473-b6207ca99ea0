@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-79ec8ccc {
  background-color: #f8f8f8;
  min-height: 100vh;
  padding: 20rpx 0;
}
.page .header.data-v-79ec8ccc {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  font-weight: 500;
  color: #3B3B3B;
  padding: 0 30rpx;
  width: 750rpx;
  height: 118rpx;
  background: #FFFFFF;
}
.page .header .right.data-v-79ec8ccc {
  display: flex;
  align-items: center;
}
.page .mid.data-v-79ec8ccc {
  margin-top: 20rpx;
  width: 750rpx;
  height: 276rpx;
  background: #FFFFFF;
  padding: 0 30rpx;
  padding-top: 40rpx;
}
.page .mid .title.data-v-79ec8ccc {
  font-size: 28rpx;
  font-weight: 500;
  color: #3B3B3B;
}
.page .mid .top.data-v-79ec8ccc {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  padding-top: 28rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #F2F3F6;
}
.page .mid .top .r_left.data-v-79ec8ccc {
  font-size: 28rpx;
  font-weight: 500;
  color: #E51837;
}
.page .mid .bottom.data-v-79ec8ccc {
  padding-top: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #999999;
}
.page .btn.data-v-79ec8ccc {
  margin: 0 auto;
  margin-top: 60rpx;
  width: 690rpx;
  height: 98rpx;
  background: #2E80FE;
  border-radius: 50rpx 50rpx 50rpx 50rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 98rpx;
  text-align: center;
}
.page .tips.data-v-79ec8ccc {
  display: block;
  font-size: 24rpx;
  font-weight: 500;
  color: #999999;
  text-align: center;
  margin-top: 20rpx;
}
/* Optimized styles for the name/ID modal content */
.slot-content.data-v-79ec8ccc {
  padding: 20rpx 0;
}
.main_item.data-v-79ec8ccc {
  margin-bottom: 32rpx;
  padding: 0 24rpx;
}
.main_item .title.data-v-79ec8ccc {
  margin-bottom: 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #1a1a1a;
  display: flex;
  align-items: center;
}
.main_item .title ._span.data-v-79ec8ccc {
  color: #E41F19;
  margin-right: 8rpx;
}
.main_item .modal-input.data-v-79ec8ccc {
  width: 100%;
  height: 80rpx;
  background: #f8f8f8;
  border: 1rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 400;
  line-height: 80rpx;
  padding: 0 24rpx;
  box-sizing: border-box;
  transition: all 0.2s ease-in-out;
}
.main_item .modal-input.data-v-79ec8ccc:focus {
  border-color: #2e80fe;
  background: #ffffff;
  box-shadow: 0 0 8rpx rgba(46, 128, 254, 0.2);
}
.main_item .modal-input.data-v-79ec8ccc:disabled {
  background: #f0f0f0;
  color: #999;
  cursor: not-allowed;
}
.main_item .card.data-v-79ec8ccc {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
}
.main_item .card .card_item.data-v-79ec8ccc {
  width: 48%;
  background: #f2fafe;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: -webkit-transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out, -webkit-transform 0.2s ease-in-out;
}
.main_item .card .card_item.data-v-79ec8ccc:hover {
  -webkit-transform: translateY(-4rpx);
          transform: translateY(-4rpx);
}
.main_item .card .card_item .top.data-v-79ec8ccc {
  height: 180rpx;
  width: 100%;
  padding-top: 20rpx;
}
.main_item .card .card_item .top .das.data-v-79ec8ccc {
  margin: 0 auto;
  width: 85%;
  height: 120rpx;
  border: 2rpx dashed #2e80fe;
  border-radius: 8rpx;
  padding: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.main_item .card .card_item .top .das .up.data-v-79ec8ccc {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.main_item .card .card_item .bottom.data-v-79ec8ccc {
  height: 60rpx;
  width: 100%;
  background: linear-gradient(90deg, #2e80fe 0%, #5ba0ff 100%);
  font-size: 24rpx;
  font-weight: 500;
  color: #ffffff;
  text-align: center;
  line-height: 60rpx;
}

