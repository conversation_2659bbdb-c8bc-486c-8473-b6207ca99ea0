<block wx:if="{{ready}}"><view class="page data-v-53648d4e"><view class="box data-v-53648d4e"><view class="title data-v-53648d4e"><image class="title-icon data-v-53648d4e" src="../static/images/8957.png" mode="aspectFill"></image><label class="_span data-v-53648d4e">订单信息</label></view><view class="info-box data-v-53648d4e"><block wx:for="{{orderDetails}}" wx:for-item="info" wx:for-index="index" wx:key="index"><view class="info-item data-v-53648d4e"><text class="label data-v-53648d4e">{{info.label}}</text><text class="{{['data-v-53648d4e','value',info.alignRight?'align-right':'']}}">{{info.value}}</text></view></block><view data-event-opts="{{[['tap',[['godh',['$event']]]]]}}" class="navigation-btn data-v-53648d4e" bindtap="__e"><image class="nav-icon data-v-53648d4e" src="../static/images/9349.png" mode="aspectFill"></image><text class="data-v-53648d4e">导航</text></view></view><block wx:for="{{$root.l2}}" wx:for-item="orderSetting" wx:for-index="orderIndex" wx:key="orderIndex"><view class="dynamic-section data-v-53648d4e"><view class="dynamic-section-wrapper data-v-53648d4e"><block wx:for="{{orderSetting.l1}}" wx:for-item="item" wx:for-index="itemIndex" wx:key="itemIndex"><view class="dynamic-section-item data-v-53648d4e"><view class="title data-v-53648d4e">{{item.$orig.problemDesc}}</view><block wx:if="{{item.m0}}"><view class="img-box data-v-53648d4e"><block wx:for="{{item.l0}}" wx:for-item="url" wx:for-index="imgIndex" wx:key="imgIndex"><image class="dynamic-image data-v-53648d4e" src="{{url.g0}}" mode="aspectFill" data-event-opts="{{[['error',[['e0',['$event']]]],['tap',[['e1',['$event']]]]]}}" data-event-params="{{({url:url.$orig,orderIndex,itemIndex,imgIndex,url:url.$orig,orderSetting:orderSetting.$orig})}}" binderror="__e" bindtap="__e"></image></block></view></block><block wx:else><block wx:if="{{item.g1}}"><view class="text-box data-v-53648d4e"><text class="data-v-53648d4e">{{item.$orig.val}}</text></view></block><block wx:else><view class="text-box data-v-53648d4e"><text class="data-v-53648d4e">无</text></view></block></block></view></block></view></view></block><view class="data-v-53648d4e"><rich-text nodes="{{getconfigs.masterNotice}}" class="data-v-53648d4e"></rich-text></view><block wx:if="{{showImageModal}}"><view data-event-opts="{{[['tap',[['closeImageModal',['$event']]]]]}}" class="image-modal data-v-53648d4e" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-content data-v-53648d4e" catchtap="__e"><image class="modal-image data-v-53648d4e" src="{{currentImage}}" mode="aspectFit"></image><view data-event-opts="{{[['tap',[['closeImageModal',['$event']]]]]}}" class="close-btn data-v-53648d4e" bindtap="__e">关闭</view></view></view></block></view><view class="bottom-quote-section data-v-53648d4e"><block wx:if="{{typeSwitch===1}}"><view data-event-opts="{{[['tap',[['handleQuote',['$event']]]]]}}" class="quote-btn data-v-53648d4e" bindtap="__e">立即报价</view></block><block wx:if="{{typeSwitch===0}}"><view data-event-opts="{{[['tap',[['handleAcceptOrder',['$event']]]]]}}" class="accept-btn data-v-53648d4e" bindtap="__e">立即接单</view></block></view><u-popup vue-id="192d9d78-1" show="{{show}}" round="{{10}}" closeable="{{true}}" data-event-opts="{{[['^close',[['close']]]]}}" bind:close="__e" class="data-v-53648d4e" bind:__l="__l" vue-slots="{{['default']}}"><view class="quote-popup-box data-v-53648d4e"><view class="popup-title data-v-53648d4e">{{isAcceptingOrder?'确认接单':'立即报价'}}</view><block wx:if="{{!isAcceptingOrder}}"><view class="popup-subtitle data-v-53648d4e">报价金额</view></block><block wx:if="{{!isAcceptingOrder}}"><view class="money-input data-v-53648d4e"><u--input vue-id="{{('192d9d78-2')+','+('192d9d78-1')}}" placeholder="请输入报价金额" prefixIcon="rmb" prefixIconStyle="font-size: 22px;color: #909399" type="digit" maxlength="5" value="{{input}}" data-event-opts="{{[['^input',[['__set_model',['','input','$event',[]]],['validateInput']]]]}}" bind:input="__e" class="data-v-53648d4e" bind:__l="__l"></u--input></view></block><block wx:else><view class="popup-content-text data-v-53648d4e"><text class="data-v-53648d4e">您确定要立即接下该订单吗？</text></view></block><view class="data-v-53648d4e"><rich-text nodes="{{getconfigs.masterNotice}}" class="data-v-53648d4e"></rich-text></view><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="{{['data-v-53648d4e','confirm-quote-btn',isAcceptingOrder?'confirm-accept-btn':'']}}" bindtap="__e">{{''+(isAcceptingOrder?'确认接单':'确认报价')+''}}</view></view></u-popup><u-modal vue-id="192d9d78-3" show="{{confirmshow}}" content="{{content}}" showCancelButton="{{true}}" data-event-opts="{{[['^confirm',[['confirmRe']]],['^cancel',[['e3']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-53648d4e" bind:__l="__l"></u-modal><u-modal vue-id="192d9d78-4" show="{{masterModalShow}}" content="成为师傅才能操作" showCancelButton="{{true}}" data-event-opts="{{[['^confirm',[['goToSettle']]],['^cancel',[['e4']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-53648d4e" bind:__l="__l"></u-modal><u-modal vue-id="192d9d78-5" show="{{showNameIdModal}}" title="请完善实名信息以确保提现安全" confirmText="保存" showCancelButton="{{true}}" contentStyle="{{({padding:'40rpx',background:'#ffffff',borderRadius:'16rpx'})}}" data-event-opts="{{[['^confirm',[['saveNameIdInfo']]],['^cancel',[['e5']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-53648d4e" bind:__l="__l" vue-slots="{{['default']}}"><view class="slot-content data-v-53648d4e"><view class="main_item data-v-53648d4e"><view class="title data-v-53648d4e"><label style="color:#E41F19;" class="_span data-v-53648d4e">*</label>姓名</view><input class="modal-input data-v-53648d4e" type="text" placeholder="请输入姓名" data-event-opts="{{[['input',[['__set_model',['$0','coachName','$event',[]],['tempForm']]]]]}}" value="{{tempForm.coachName}}" bindinput="__e"/></view><view class="main_item data-v-53648d4e"><view class="title data-v-53648d4e"><label style="color:#E41F19;" class="_span data-v-53648d4e">*</label>身份证号</view><input class="modal-input data-v-53648d4e" type="text" placeholder="请输入身份证号" data-event-opts="{{[['input',[['__set_model',['$0','idCode','$event',[]],['tempForm']]]]]}}" value="{{tempForm.idCode}}" bindinput="__e"/></view><view class="main_item data-v-53648d4e"><view class="title data-v-53648d4e"><label style="color:#E41F19;" class="_span data-v-53648d4e">*</label>上传身份证照片</view><view class="card data-v-53648d4e"><view class="card_item data-v-53648d4e"><view class="top data-v-53648d4e"><view class="das data-v-53648d4e"><view class="up data-v-53648d4e"><upload vue-id="{{('192d9d78-6')+','+('192d9d78-5')}}" imagelist="{{tempForm.id_card1}}" imgtype="id_card1" imgclass="id_card_box" text="身份证人像面" imgsize="{{1}}" data-event-opts="{{[['^upload',[['imgUploadTemp']]],['^del',[['imgUploadTemp']]]]}}" bind:upload="__e" bind:del="__e" class="data-v-53648d4e" bind:__l="__l"></upload></view></view></view><view class="bottom data-v-53648d4e">拍摄人像面</view></view><view class="card_item data-v-53648d4e"><view class="top data-v-53648d4e"><view class="das data-v-53648d4e"><view class="up data-v-53648d4e"><upload vue-id="{{('192d9d78-7')+','+('192d9d78-5')}}" imagelist="{{tempForm.id_card2}}" imgtype="id_card2" imgclass="id_card_box" text="身份证国徽面" imgsize="{{1}}" data-event-opts="{{[['^upload',[['imgUploadTemp']]],['^del',[['imgUploadTemp']]]]}}" bind:upload="__e" bind:del="__e" class="data-v-53648d4e" bind:__l="__l"></upload></view></view></view><view class="bottom data-v-53648d4e">拍摄国徽面</view></view></view></view></view></u-modal></view></block>