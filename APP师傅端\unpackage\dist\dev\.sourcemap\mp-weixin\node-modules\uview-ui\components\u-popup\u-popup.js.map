{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-popup/u-popup.vue?3d34", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-popup/u-popup.vue?c226", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-popup/u-popup.vue?3a4e", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-popup/u-popup.vue?29ba", "uni-app:///node_modules/uview-ui/components/u-popup/u-popup.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-popup/u-popup.vue?1a06", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-popup/u-popup.vue?918c"], "names": ["name", "mixins", "data", "overlayDuration", "watch", "show", "computed", "transitionStyle", "zIndex", "position", "display", "style", "bottom", "top", "left", "right", "alignItems", "contentStyle", "uni", "safeAreaInsets", "methods", "overlayClick", "close", "afterEnter", "clickHandler", "retryComputedComponentRect", "i", "child"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wSAEN;AACP,KAAK;AACL;AACA,aAAa,0TAEN;AACP,KAAK;AACL;AACA,aAAa,0TAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,gUAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9DA;AAAA;AAAA;AAAA;AAAs1B,CAAgB,s2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC+C12B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAvBA,eAwBA;EACAA;EACAC;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;QAEA;QACA;MAEA;IACA;EACA;EACAC;IACAC;MACA;QACAC;QACAC;QACAC;MACA;MACAC;MACA;QACA;UACAC;UACAC;QACA;MACA;QACA;UACAD;UACAC;QACA;MACA;QACA;UACAC;UACAC;QACA;MACA;QACA;UACAD;UACAC;QACA;MACA;QACA;UACAC;UACA;UACAH;UACAC;UACAC;UACAH;QACA;MACA;IACA;IACAK;MACA;MACA;MACA;MACA,kBAEAC;QADAC;MAEA;QACAR;MACA;MACA;MACA;QACAA;MACA;MACA;QACA;QACA;UACAA;UACAA;QACA;UACAA;UACAA;QACA;UACAA;QACA;MACA;MACA;IACA;IACAF;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;IACA;EACA;EACAW;IACA;IACAC;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;IACA;IAEAC;MAAA;MACA;MACA,6GACA,qGACA,yGACA,sBACA;MACA;MAAA,2BACAC;QACA;QACA;QACA;QACA;QACA;UACA;UACAR;YACAS;UACA;QACA;QACA;QACA;UACA;QACA;MAAA;MAdA;QAAA;MAeA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AChOA;AAAA;AAAA;AAAA;AAA6lD,CAAgB,ijDAAG,EAAC,C;;;;;;;;;;;ACAjnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-popup/u-popup.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-popup.vue?vue&type=template&id=52d4ddd1&scoped=true&\"\nvar renderjs\nimport script from \"./u-popup.vue?vue&type=script&lang=js&\"\nexport * from \"./u-popup.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-popup.vue?vue&type=style&index=0&id=52d4ddd1&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"52d4ddd1\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-popup/u-popup.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-popup.vue?vue&type=template&id=52d4ddd1&scoped=true&\"", "var components\ntry {\n  components = {\n    uOverlay: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-overlay/u-overlay\" */ \"uview-ui/components/u-overlay/u-overlay.vue\"\n      )\n    },\n    uTransition: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-transition/u-transition\" */ \"uview-ui/components/u-transition/u-transition.vue\"\n      )\n    },\n    uStatusBar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-status-bar/u-status-bar\" */ \"uview-ui/components/u-status-bar/u-status-bar.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uSafeBottom: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-safe-bottom/u-safe-bottom\" */ \"uview-ui/components/u-safe-bottom/u-safe-bottom.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.contentStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-popup.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-popup.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-popup\">\r\n\t\t<u-overlay\r\n\t\t\t:show=\"show\"\r\n\t\t\t@click=\"overlayClick\"\r\n\t\t\tv-if=\"overlay\"\r\n\t\t\t:duration=\"overlayDuration\"\r\n\t\t\t:customStyle=\"overlayStyle\"\r\n\t\t\t:opacity=\"overlayOpacity\"\r\n\t\t></u-overlay>\r\n\t\t<u-transition\r\n\t\t\t:show=\"show\"\r\n\t\t\t:customStyle=\"transitionStyle\"\r\n\t\t\t:mode=\"position\"\r\n\t\t\t:duration=\"duration\"\r\n\t\t\t@afterEnter=\"afterEnter\"\r\n\t\t\t@click=\"clickHandler\"\r\n\t\t>\r\n\t\t\t<view\r\n\t\t\t\tclass=\"u-popup__content\"\r\n\t\t\t\t:style=\"[contentStyle]\"\r\n\t\t\t\**********=\"noop\"\r\n\t\t\t>\r\n\t\t\t\t<u-status-bar v-if=\"safeAreaInsetTop\"></u-status-bar>\r\n\t\t\t\t<slot></slot>\r\n\t\t\t\t<view\r\n\t\t\t\t\tv-if=\"closeable\"\r\n\t\t\t\t\**********=\"close\"\r\n\t\t\t\t\tclass=\"u-popup__content__close\"\r\n\t\t\t\t\t:class=\"['u-popup__content__close--' + closeIconPos]\"\r\n\t\t\t\t\thover-class=\"u-popup__content__close--hover\"\r\n\t\t\t\t\thover-stay-time=\"150\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<u-icon\r\n\t\t\t\t\t\tname=\"close\"\r\n\t\t\t\t\t\tcolor=\"#909399\"\r\n\t\t\t\t\t\tsize=\"18\"\r\n\t\t\t\t\t\tbold\r\n\t\t\t\t\t></u-icon>\r\n\t\t\t\t</view>\r\n\t\t\t\t<u-safe-bottom v-if=\"safeAreaInsetBottom\"></u-safe-bottom>\r\n\t\t\t</view>\r\n\t\t</u-transition>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport props from './props.js';\r\n\r\n\t/**\r\n\t * popup 弹窗\r\n\t * @description 弹出层容器，用于展示弹窗、信息提示等内容，支持上、下、左、右和中部弹出。组件只提供容器，内部内容由用户自定义\r\n\t * @tutorial https://www.uviewui.com/components/popup.html\r\n\t * @property {Boolean}\t\t\tshow\t\t\t\t是否展示弹窗 (默认 false )\r\n\t * @property {Boolean}\t\t\toverlay\t\t\t\t是否显示遮罩 （默认 true ）\r\n\t * @property {String}\t\t\tmode\t\t\t\t弹出方向（默认 'bottom' ）\r\n\t * @property {String | Number}\tduration\t\t\t动画时长，单位ms （默认 300 ）\r\n\t * @property {String | Number}\toverlayDuration\t\t\t遮罩层动画时长，单位ms （默认 350 ）\r\n\t * @property {Boolean}\t\t\tcloseable\t\t\t是否显示关闭图标（默认 false ）\r\n\t * @property {Object | String}\toverlayStyle\t\t自定义遮罩的样式\r\n\t * @property {String | Number}\toverlayOpacity\t\t遮罩透明度，0-1之间（默认 0.5）\r\n\t * @property {Boolean}\t\t\tcloseOnClickOverlay\t点击遮罩是否关闭弹窗 （默认  true ）\r\n\t * @property {String | Number}\tzIndex\t\t\t\t层级 （默认 10075 ）\r\n\t * @property {Boolean}\t\t\tsafeAreaInsetBottom\t是否为iPhoneX留出底部安全距离 （默认 true ）\r\n\t * @property {Boolean}\t\t\tsafeAreaInsetTop\t是否留出顶部安全距离（状态栏高度） （默认 false ）\r\n\t * @property {String}\t\t\tcloseIconPos\t\t自定义关闭图标位置（默认 'top-right' ）\r\n\t * @property {String | Number}\tround\t\t\t\t圆角值（默认 0）\r\n\t * @property {Boolean}\t\t\tzoom\t\t\t\t当mode=center时 是否开启缩放（默认 true ）\r\n\t * @property {Object}\t\t\tcustomStyle\t\t\t组件的样式，对象形式\r\n\t * @event {Function} open 弹出层打开\r\n\t * @event {Function} close 弹出层收起\r\n\t * @example <u-popup v-model=\"show\"><text>出淤泥而不染，濯清涟而不妖</text></u-popup>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-popup',\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\toverlayDuration: Number(this.duration) + 50\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tshow(newValue, oldValue) {\r\n\t\t\t\tif (newValue === true) {\r\n\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\tconst children = this.$children\r\n\t\t\t\t\tthis.retryComputedComponentRect(children)\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\ttransitionStyle() {\r\n\t\t\t\tconst style = {\r\n\t\t\t\t\tzIndex: this.zIndex,\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\tdisplay: 'flex',\r\n\t\t\t\t}\r\n\t\t\t\tstyle[this.mode] = 0\r\n\t\t\t\tif (this.mode === 'left') {\r\n\t\t\t\t\treturn uni.$u.deepMerge(style, {\r\n\t\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t})\r\n\t\t\t\t} else if (this.mode === 'right') {\r\n\t\t\t\t\treturn uni.$u.deepMerge(style, {\r\n\t\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t})\r\n\t\t\t\t} else if (this.mode === 'top') {\r\n\t\t\t\t\treturn uni.$u.deepMerge(style, {\r\n\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\tright: 0\r\n\t\t\t\t\t})\r\n\t\t\t\t} else if (this.mode === 'bottom') {\r\n\t\t\t\t\treturn uni.$u.deepMerge(style, {\r\n\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t})\r\n\t\t\t\t} else if (this.mode === 'center') {\r\n\t\t\t\t\treturn uni.$u.deepMerge(style, {\r\n\t\t\t\t\t\talignItems: 'center',\r\n\t\t\t\t\t\t'justify-content': 'center',\r\n\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t\tbottom: 0\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcontentStyle() {\r\n\t\t\t\tconst style = {}\r\n\t\t\t\t// 通过设备信息的safeAreaInsets值来判断是否需要预留顶部状态栏和底部安全局的位置\r\n\t\t\t\t// 不使用css方案，是因为nvue不支持css的iPhoneX安全区查询属性\r\n\t\t\t\tconst {\r\n\t\t\t\t\tsafeAreaInsets\r\n\t\t\t\t} = uni.$u.sys()\r\n\t\t\t\tif (this.mode !== 'center') {\r\n\t\t\t\t\tstyle.flex = 1\r\n\t\t\t\t}\r\n\t\t\t\t// 背景色，一般用于设置为transparent，去除默认的白色背景\r\n\t\t\t\tif (this.bgColor) {\r\n\t\t\t\t\tstyle.backgroundColor = this.bgColor\r\n\t\t\t\t}\r\n\t\t\t\tif(this.round) {\r\n\t\t\t\t\tconst value = uni.$u.addUnit(this.round)\r\n\t\t\t\t\tif(this.mode === 'top') {\r\n\t\t\t\t\t\tstyle.borderBottomLeftRadius = value\r\n\t\t\t\t\t\tstyle.borderBottomRightRadius = value\r\n\t\t\t\t\t} else if(this.mode === 'bottom') {\r\n\t\t\t\t\t\tstyle.borderTopLeftRadius = value\r\n\t\t\t\t\t\tstyle.borderTopRightRadius = value\r\n\t\t\t\t\t} else if(this.mode === 'center') {\r\n\t\t\t\t\t\tstyle.borderRadius = value\r\n\t\t\t\t\t} \r\n\t\t\t\t}\r\n\t\t\t\treturn uni.$u.deepMerge(style, uni.$u.addStyle(this.customStyle))\r\n\t\t\t},\r\n\t\t\tposition() {\r\n\t\t\t\tif (this.mode === 'center') {\r\n\t\t\t\t\treturn this.zoom ? 'fade-zoom' : 'fade'\r\n\t\t\t\t}\r\n\t\t\t\tif (this.mode === 'left') {\r\n\t\t\t\t\treturn 'slide-left'\r\n\t\t\t\t}\r\n\t\t\t\tif (this.mode === 'right') {\r\n\t\t\t\t\treturn 'slide-right'\r\n\t\t\t\t}\r\n\t\t\t\tif (this.mode === 'bottom') {\r\n\t\t\t\t\treturn 'slide-up'\r\n\t\t\t\t}\r\n\t\t\t\tif (this.mode === 'top') {\r\n\t\t\t\t\treturn 'slide-down'\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 点击遮罩\r\n\t\t\toverlayClick() {\r\n\t\t\t\tif (this.closeOnClickOverlay) {\r\n\t\t\t\t\tthis.$emit('close')\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tclose(e) {\r\n\t\t\t\tthis.$emit('close')\r\n\t\t\t},\r\n\t\t\tafterEnter() {\r\n\t\t\t\tthis.$emit('open')\r\n\t\t\t},\r\n\t\t\tclickHandler() {\r\n\t\t\t\t// 由于中部弹出时，其u-transition占据了整个页面相当于遮罩，此时需要发出遮罩点击事件，是否无法通过点击遮罩关闭弹窗\r\n\t\t\t\tif(this.mode === 'center') {\r\n\t\t\t\t\tthis.overlayClick()\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('click')\r\n\t\t\t},\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tretryComputedComponentRect(children) {\r\n\t\t\t\t// 组件内部需要计算节点的组件\r\n\t\t\t\tconst names = ['u-calendar-month', 'u-album', 'u-collapse-item', 'u-dropdown', 'u-index-item', 'u-index-list',\r\n\t\t\t\t\t'u-line-progress', 'u-list-item', 'u-rate', 'u-read-more', 'u-row', 'u-row-notice', 'u-scroll-list',\r\n\t\t\t\t\t'u-skeleton', 'u-slider', 'u-steps-item', 'u-sticky', 'u-subsection', 'u-swipe-action-item', 'u-tabbar',\r\n\t\t\t\t\t'u-tabs', 'u-tooltip'\r\n\t\t\t\t]\r\n\t\t\t\t// 历遍所有的子组件节点\r\n\t\t\t\tfor (let i = 0; i < children.length; i++) {\r\n\t\t\t\t\tconst child = children[i]\r\n\t\t\t\t\t// 拿到子组件的子组件\r\n\t\t\t\t\tconst grandChild = child.$children\r\n\t\t\t\t\t// 判断如果在需要重新初始化的组件数组中名中，并且存在init方法的话，则执行\r\n\t\t\t\t\tif (names.includes(child.$options.name) && typeof child?.init === 'function') {\r\n\t\t\t\t\t\t// 需要进行一定的延时，因为初始化页面需要时间\r\n\t\t\t\t\t\tuni.$u.sleep(50).then(() => {\r\n\t\t\t\t\t\t\tchild.init()\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 如果子组件还有孙组件，进行递归历遍\r\n\t\t\t\t\tif (grandChild.length) {\r\n\t\t\t\t\t\tthis.retryComputedComponentRect(grandChild)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/components.scss\";\r\n\t$u-popup-flex:1 !default;\r\n\t$u-popup-content-background-color: #fff !default;\r\n\r\n\t.u-popup {\r\n\t\tflex: $u-popup-flex;\r\n\r\n\t\t&__content {\r\n\t\t\tbackground-color: $u-popup-content-background-color;\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t&--round-top {\r\n\t\t\t\tborder-top-left-radius: 0;\r\n\t\t\t\tborder-top-right-radius: 0;\r\n\t\t\t\tborder-bottom-left-radius: 10px;\r\n\t\t\t\tborder-bottom-right-radius: 10px;\r\n\t\t\t}\r\n\r\n\t\t\t&--round-left {\r\n\t\t\t\tborder-top-left-radius: 0;\r\n\t\t\t\tborder-top-right-radius: 10px;\r\n\t\t\t\tborder-bottom-left-radius: 0;\r\n\t\t\t\tborder-bottom-right-radius: 10px;\r\n\t\t\t}\r\n\r\n\t\t\t&--round-right {\r\n\t\t\t\tborder-top-left-radius: 10px;\r\n\t\t\t\tborder-top-right-radius: 0;\r\n\t\t\t\tborder-bottom-left-radius: 10px;\r\n\t\t\t\tborder-bottom-right-radius: 0;\r\n\t\t\t}\r\n\r\n\t\t\t&--round-bottom {\r\n\t\t\t\tborder-top-left-radius: 10px;\r\n\t\t\t\tborder-top-right-radius: 10px;\r\n\t\t\t\tborder-bottom-left-radius: 0;\r\n\t\t\t\tborder-bottom-right-radius: 0;\r\n\t\t\t}\r\n\r\n\t\t\t&--round-center {\r\n\t\t\t\tborder-top-left-radius: 10px;\r\n\t\t\t\tborder-top-right-radius: 10px;\r\n\t\t\t\tborder-bottom-left-radius: 10px;\r\n\t\t\t\tborder-bottom-right-radius: 10px;\r\n\t\t\t}\r\n\r\n\t\t\t&__close {\r\n\t\t\t\tposition: absolute;\r\n\r\n\t\t\t\t&--hover {\r\n\t\t\t\t\topacity: 0.4;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&__close--top-left {\r\n\t\t\t\ttop: 15px;\r\n\t\t\t\tleft: 15px;\r\n\t\t\t}\r\n\r\n\t\t\t&__close--top-right {\r\n\t\t\t\ttop: 15px;\r\n\t\t\t\tright: 15px;\r\n\t\t\t}\r\n\r\n\t\t\t&__close--bottom-left {\r\n\t\t\t\tbottom: 15px;\r\n\t\t\t\tleft: 15px;\r\n\t\t\t}\r\n\r\n\t\t\t&__close--bottom-right {\r\n\t\t\t\tright: 15px;\r\n\t\t\t\tbottom: 15px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-popup.vue?vue&type=style&index=0&id=52d4ddd1&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-popup.vue?vue&type=style&index=0&id=52d4ddd1&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755159810852\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}